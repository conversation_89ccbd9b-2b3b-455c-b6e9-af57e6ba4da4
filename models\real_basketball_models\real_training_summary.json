{"points": {"prop_name": "points", "input_size": 30, "train_r2": 0.994574358874793, "test_r2": 0.9883675765163588, "train_rmse": 10.38548655834295, "test_rmse": 15.555032289146583, "best_val_loss": 241.95901489257812, "model_path": "models\\real_basketball_models\\real_points_model.pt", "scaler_path": "models\\real_basketball_models\\real_points_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_points_scaler_params.json", "feature_count": 30}, "rebounds": {"prop_name": "rebounds", "input_size": 30, "train_r2": 0.9890468899600536, "test_r2": 0.9850899994775281, "train_rmse": 6.43369616590581, "test_rmse": 7.6940967981478385, "best_val_loss": 42.131927490234375, "model_path": "models\\real_basketball_models\\real_rebounds_model.pt", "scaler_path": "models\\real_basketball_models\\real_rebounds_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_rebounds_scaler_params.json", "feature_count": 30}, "assists": {"prop_name": "assists", "input_size": 30, "train_r2": 0.9903619559127279, "test_r2": 0.9881684426032213, "train_rmse": 3.8562141158129357, "test_rmse": 4.610821782443083, "best_val_loss": 11.442885398864746, "model_path": "models\\real_basketball_models\\real_assists_model.pt", "scaler_path": "models\\real_basketball_models\\real_assists_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_assists_scaler_params.json", "feature_count": 30}, "steals": {"prop_name": "steals", "input_size": 30, "train_r2": 0.9963790286871826, "test_r2": 0.9938260176791637, "train_rmse": 0.740223734230131, "test_rmse": 0.9635084003606652, "best_val_loss": 0.8272939324378967, "model_path": "models\\real_basketball_models\\real_steals_model.pt", "scaler_path": "models\\real_basketball_models\\real_steals_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_steals_scaler_params.json", "feature_count": 30}, "blocks": {"prop_name": "blocks", "input_size": 30, "train_r2": 0.9929926749726573, "test_r2": 0.9913428094990822, "train_rmse": 0.8040338532134569, "test_rmse": 0.9978115819779921, "best_val_loss": 0.39866411685943604, "model_path": "models\\real_basketball_models\\real_blocks_model.pt", "scaler_path": "models\\real_basketball_models\\real_blocks_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_blocks_scaler_params.json", "feature_count": 30}, "threes": {"prop_name": "threes", "input_size": 30, "train_r2": 0.995186118210093, "test_r2": 0.994240899721542, "train_rmse": 1.0898167910207526, "test_rmse": 1.223360448164022, "best_val_loss": 1.3940538167953491, "model_path": "models\\real_basketball_models\\real_threes_model.pt", "scaler_path": "models\\real_basketball_models\\real_threes_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_threes_scaler_params.json", "feature_count": 30}, "game_prediction": {"model_type": "game_prediction", "input_size": 10, "train_accuracy": 0.601995587348938, "test_accuracy": 0.6017699241638184, "best_val_accuracy": 0.6084070801734924, "model_path": "models\\real_basketball_models\\real_game_model.pt", "scaler_path": "models\\real_basketball_models\\real_game_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_game_scaler_params.json", "feature_count": 10}, "team_total": {"model_type": "team_total", "input_size": 9, "train_r2": 0.8096903312670103, "test_r2": 0.741212939855687, "train_rmse": 4.782790187422952, "test_rmse": 5.6509026170353085, "best_val_loss": 0.2497749775648117, "model_path": "models\\real_basketball_models\\real_team_total_model.pt", "feature_scaler_path": "models\\real_basketball_models\\real_team_total_feature_scaler.pkl", "target_scaler_path": "models\\real_basketball_models\\real_team_total_target_scaler.pkl", "feature_scaler_params_path": "models\\real_basketball_models\\real_team_total_feature_scaler_params.json", "target_scaler_params_path": "models\\real_basketball_models\\real_team_total_target_scaler_params.json", "feature_count": 9}}