{"points": {"prop_name": "points", "input_size": 30, "train_r2": 0.9941017918640221, "test_r2": 0.9871771863730537, "train_rmse": 10.828327009622527, "test_rmse": 16.331552816172792, "best_val_loss": 266.71966552734375, "model_path": "models\\real_basketball_models\\real_points_model.pt", "scaler_path": "models\\real_basketball_models\\real_points_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_points_scaler_params.json", "feature_count": 30}, "rebounds": {"prop_name": "rebounds", "input_size": 30, "train_r2": 0.9931878591256084, "test_r2": 0.9898738797817261, "train_rmse": 5.073803365270547, "test_rmse": 6.340745993964782, "best_val_loss": 40.20505905151367, "model_path": "models\\real_basketball_models\\real_rebounds_model.pt", "scaler_path": "models\\real_basketball_models\\real_rebounds_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_rebounds_scaler_params.json", "feature_count": 30}, "assists": {"prop_name": "assists", "input_size": 30, "train_r2": 0.9937498142339963, "test_r2": 0.9928207070958124, "train_rmse": 3.105368537092911, "test_rmse": 3.5916847844486597, "best_val_loss": 12.900198936462402, "model_path": "models\\real_basketball_models\\real_assists_model.pt", "scaler_path": "models\\real_basketball_models\\real_assists_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_assists_scaler_params.json", "feature_count": 30}, "steals": {"prop_name": "steals", "input_size": 30, "train_r2": 0.996553554298039, "test_r2": 0.9919487904918578, "train_rmse": 0.7221645808566213, "test_rmse": 1.1002804049717072, "best_val_loss": 0.7938469648361206, "model_path": "models\\real_basketball_models\\real_steals_model.pt", "scaler_path": "models\\real_basketball_models\\real_steals_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_steals_scaler_params.json", "feature_count": 30}, "blocks": {"prop_name": "blocks", "input_size": 30, "train_r2": 0.9966542059066698, "test_r2": 0.995331593716484, "train_rmse": 0.5555815418604947, "test_rmse": 0.7327306797174749, "best_val_loss": 0.4540371596813202, "model_path": "models\\real_basketball_models\\real_blocks_model.pt", "scaler_path": "models\\real_basketball_models\\real_blocks_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_blocks_scaler_params.json", "feature_count": 30}, "threes": {"prop_name": "threes", "input_size": 30, "train_r2": 0.9928023269117102, "test_r2": 0.9918035976072613, "train_rmse": 1.3326062410460267, "test_rmse": 1.4594484204741782, "best_val_loss": 1.1179702281951904, "model_path": "models\\real_basketball_models\\real_threes_model.pt", "scaler_path": "models\\real_basketball_models\\real_threes_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_threes_scaler_params.json", "feature_count": 30}, "game_prediction": {"model_type": "game_prediction", "input_size": 30, "train_accuracy": 0.8224999904632568, "test_accuracy": 0.7900000214576721, "best_val_accuracy": 0.800000011920929, "model_path": "models\\real_basketball_models\\real_game_model.pt", "scaler_path": "models\\real_basketball_models\\real_game_scaler.pkl", "scaler_params_path": "models\\real_basketball_models\\real_game_scaler_params.json", "feature_count": 30}, "team_total": {"model_type": "team_total", "input_size": 9, "train_r2": 0.21960283109705725, "test_r2": 0.19050303057988704, "train_rmse": 9.048939266084693, "test_rmse": 9.125168374611315, "best_val_loss": 0.7893734574317932, "model_path": "models\\real_basketball_models\\real_team_total_model.pt", "feature_scaler_path": "models\\real_basketball_models\\real_team_total_feature_scaler.pkl", "target_scaler_path": "models\\real_basketball_models\\real_team_total_target_scaler.pkl", "feature_scaler_params_path": "models\\real_basketball_models\\real_team_total_feature_scaler_params.json", "target_scaler_params_path": "models\\real_basketball_models\\real_team_total_target_scaler_params.json", "feature_count": 9}}