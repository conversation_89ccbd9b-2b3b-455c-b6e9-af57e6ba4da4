#!/usr/bin/env python3
"""
Debug script to check what games are being returned from the odds API
"""

import asyncio
import sys
from datetime import datetime

# Add project root to path
sys.path.append('.')

from vault_oracle.wells.expert_odds_integration import create_expert_odds_integrator

async def debug_odds_games():
    """Debug what games are being returned from odds API"""
    print("🔍 Debugging odds API games...")
    
    try:
        # Create odds integrator
        integrator = create_expert_odds_integrator()
        
        # Fetch WNBA odds data
        print("📡 Fetching WNBA odds data...")
        odds_data = await integrator.fetch_odds_data("basketball_wnba")
        
        print(f"✅ Retrieved {len(odds_data)} WNBA games from odds API")
        print()
        
        # Show all games with their commence times
        for i, odds in enumerate(odds_data, 1):
            print(f"🏀 Game {i}:")
            print(f"   ID: {odds.titan_clash_id}")
            print(f"   Teams: {odds.away_team} @ {odds.home_team}")
            print(f"   Commence Time: {odds.commence_time}")
            print(f"   Date: {odds.commence_time.strftime('%Y-%m-%d')}")
            print(f"   Time: {odds.commence_time.strftime('%H:%M:%S %Z')}")
            print()
        
        # Check what today's date is
        today = datetime.now()
        today_str = today.strftime('%Y-%m-%d')
        print(f"📅 Today's date: {today_str}")
        print(f"📅 Current time: {today.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        # Filter for today's games
        todays_games = []
        for odds in odds_data:
            game_date = odds.commence_time.strftime('%Y-%m-%d')
            if game_date == today_str:
                todays_games.append(odds)
        
        print(f"🎯 Games for today ({today_str}): {len(todays_games)}")
        for game in todays_games:
            print(f"   {game.away_team} @ {game.home_team} at {game.commence_time}")
        
        # Also check for July 5th specifically
        july_5_str = "2025-07-05"
        july_5_games = []
        for odds in odds_data:
            game_date = odds.commence_time.strftime('%Y-%m-%d')
            if game_date == july_5_str:
                july_5_games.append(odds)
        
        print(f"🎯 Games for July 5th ({july_5_str}): {len(july_5_games)}")
        for game in july_5_games:
            print(f"   {game.away_team} @ {game.home_team} at {game.commence_time}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_odds_games())
