import os
import sys
import io
import logging
import asyncio
"""
Unicode/Emoji Logging Fix for Windows Consoles
Ensures all logging output is UTF-8 encoded, preventing UnicodeEncodeError on Windows.
"""
# --- UTF-8 Console Patch (MUST be before any logging/print) ---
if os.name == 'nt':
    # On Windows, re-wrap sys.stdout/sys.stderr with UTF-8 encoding if not already
    if sys.stdout.encoding.lower() != 'utf-8':
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
    if sys.stderr.encoding.lower() != 'utf-8':
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')

# --- Logger Setup Utility ---
def setup_utf8_logger(name):
    logger = logging.getLogger(name)
    if not logger.handlers:
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(logging.Formatter(
            "%(asctime)s %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        ))
        logger.addHandler(handler)
    logger.propagate = False
    return logger

# Use the UTF-8 safe logger for this module
logger = setup_utf8_logger(__name__)
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date, timezone
from typing import Dict, List, Tuple, Optional, Any, Union, Literal, Type
import json
import sqlite3
from pathlib import Path
import re # For regex in season parsing
from dataclasses import dataclass, field
from vault_oracle.wells.nba_api_connector import BasketballDataConnector, APIConnectorConfig
from vault_oracle.wells.nba_api_connector import PlayerProfileV2
from src.data.wnba_data_integration import wnba_service
import argparse

#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=ef01b2c3-4567-89ab-cdef-0123456789ab | DATE=2025-06-27
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary data loading algorithms and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Basketball Data Loader Business Value Documentation
===============================================================================

basketball_data_loader.py
-------------------------
Provides advanced NBA/WNBA data loading and preprocessing for neural training and analytics.

Business Value:
- Enables robust, scalable, and efficient data ingestion for analytics and model training.
- Supports rapid integration of new data sources and feature pipelines.
- Accelerates development of new analytics and machine learning features.

Extension Points for Plugins & Custom Data Loaders:
---------------------------------------------------
- Subclass `BasketballDataLoader` to add new data loading or preprocessing logic.
- Register data loader plugins via a plugin registry or callback system.
- Add new endpoints or data sources by extending the loader class.
- Use the config system to enable/disable features per environment.

For further details, see module-level docstrings and architecture documentation.
"""

"""
 HYPER MEDUSA NEURAL VAULT - Basketball Data Loader 
═══════════════════════════════════════════════════════════════════════════════════

Advanced Basketball Data Loader with NBA API integration for neural training.
Provides comprehensive real basketball data for training the Neural Basketball Core.

Features:
- NBA API integration via existing BasketballDataConnector
- Historical game data retrieval and processing
- Feature engineering for machine learning
- Data validation and quality checks
- Caching for performance optimization
- Support for both NBA and WNBA data

 Elite Basketball Intelligence Data Pipeline 
"""

# Add project root to path BEFORE any vault_oracle imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


# --- NBA API endpoint imports (add these at the top of your file, after other imports) ---
from nba_api.stats.endpoints import (
    ScheduleLeagueV2, boxscoretraditionalv2, boxscoreadvancedv2, shotchartdetail,
    playerdashboardbygeneralsplits, teamdashboardbygeneralsplits, commonallplayers,
    leaguedashteamstats, playergamelog, leaguegamefinder, leagueleaders, teamdetails,
    playerprofilev2, leaguedashplayerclutch, leaguedashteamclutch,
    leaguedashplayerptshot, leaguedashplayershotlocations, leaguedashoppptshot,
    leaguedashptdefend, leaguedashptteamdefend, boxscorescoringv2, boxscoremiscv2,
    boxscorefourfactorsv2, boxscoreusagev2, commonplayerinfo, commonteamyears,
    leaguestandingsv3, teamgamelog, playbyplayv2, gamerotation, leaguedashlineups,
    leagueplayerondetails, teamplayeronoffdetails, teamplayeronoffsummary,
    playervsplayer, teamvsplayer, teamandplayersvsplayers, drafthistory,
    leaguehustlestatsplayer, leaguehustlestatsteam
    
)
# --- END NBA API endpoint imports ---


# Configure logger
import sys
# Ensure sys.stdout uses UTF-8 encoding (Python 3.7+)
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')

logger = logging.getLogger("basketball_data_loader")
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

@dataclass
class DataLoaderConfig:
    """Configuration for the BasketballDataLoader."""
    historical_data_years: int = 5
    cache_enabled: bool = True
    cache_dir: str = "data_cache"
    data_source_priority: List[str] = field(default_factory=lambda: ["nba_api", "local_db"])
    # You might want to define other loader-specific configurations here

class BasketballDataLoader:
    """
    Manages loading and initial processing of basketball data from various sources,
    primarily through the BasketballDataConnector.
    """

    def __init__(self, config: DataLoaderConfig = DataLoaderConfig()):
        self.config = config
        self.connector_config = APIConnectorConfig(output_dir=self.config.cache_dir)
        self.connector = BasketballDataConnector(self.connector_config)
        logger.info("BasketballDataLoader initialized with NBA API connector.")
        os.makedirs(self.config.cache_dir, exist_ok=True) # Ensure cache directory exists

    async def get_players(self, season: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches all players for a given season."""
        return await self.connector.get_all_players(season=season, league_id=league_id)

    async def get_player_game_logs(self, player_id: int, season: str, season_type: Optional[str] = None, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches game logs for a specific player."""
        return await self.connector.get_player_game_log(player_id=player_id, season=season, season_type=season_type, league_id=league_id)

    async def get_team_statistics(self, season: str, season_type: Optional[str] = None, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches team statistics."""
        return await self.connector.get_team_stats(season=season, season_type=season_type, league_id=league_id)

    async def get_schedule(self, season: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches the league schedule."""
        return await self.connector.get_league_schedule(season=season, league_id=league_id)

    def load_training_data(self, league: str = "NBA", data_source: str = "auto") -> Optional[pd.DataFrame]:
        """
        Load training data for neural network training

        Args:
            league: "NBA" or "WNBA"
            data_source: "auto", "csv", "database"

        Returns:
            DataFrame with training data or None if failed
        """
        logger.info(f"🏀 Loading training data for {league}...")

        try:
            # Try different data sources with comprehensive coverage priority
            if data_source == "auto" or data_source == "csv":
                # For WNBA, combine historical data with current season data
                if league.upper() == "WNBA":
                    # Load comprehensive historical data (2015-2024)
                    historical_data = self._load_comprehensive_wnba_historical()

                    # Load current season data from CSV (includes 2025)
                    csv_data = self._load_from_csv(league)

                    # Combine both datasets for maximum coverage
                    combined_data = None
                    if historical_data is not None and csv_data is not None:
                        # Check if CSV has 2025 data that historical doesn't
                        if 'season' in csv_data.columns:
                            csv_2025 = csv_data[csv_data['season'].astype(str).str.contains('2025', na=False)]
                            if not csv_2025.empty:
                                logger.info(f"🎯 Found {len(csv_2025)} records of 2025 WNBA data in CSV")
                                # Combine historical + 2025 data
                                try:
                                    # Align columns between datasets
                                    common_cols = list(set(historical_data.columns) & set(csv_2025.columns))
                                    if common_cols:
                                        hist_subset = historical_data[common_cols]
                                        csv_subset = csv_2025[common_cols]
                                        combined_data = pd.concat([hist_subset, csv_subset], ignore_index=True)
                                        logger.info(f"🏆 Combined dataset: {len(combined_data)} records (historical + 2025)")
                                except Exception as e:
                                    logger.warning(f"⚠️ Could not combine datasets: {e}")

                    # Use combined data if available, otherwise fall back to individual datasets
                    if combined_data is not None and not combined_data.empty:
                        logger.info(f"✅ Using combined WNBA dataset: {len(combined_data)} records")
                        return self._preprocess_training_data(combined_data, league)
                    elif historical_data is not None and not historical_data.empty:
                        logger.info(f"🏆 Using comprehensive historical WNBA data: {len(historical_data)} records")
                        return self._preprocess_training_data(historical_data, league)
                    elif csv_data is not None and not csv_data.empty:
                        logger.info(f"📄 Using CSV WNBA data: {len(csv_data)} records")
                        return self._preprocess_training_data(csv_data, league)

                # For non-WNBA leagues, use standard CSV loading
                csv_data = self._load_from_csv(league)
                if csv_data is not None and not csv_data.empty:
                    logger.info(f"✅ Loaded {len(csv_data)} records from CSV for {league}")
                    return self._preprocess_training_data(csv_data, league)

            if data_source == "auto" or data_source == "database":
                # Fallback to database
                db_data = self._load_from_database(league)
                if db_data is not None and not db_data.empty:
                    logger.info(f"✅ Loaded {len(db_data)} records from database for {league}")
                    return self._preprocess_training_data(db_data, league)

            logger.warning(f"⚠️ No training data found for {league}")
            return None

        except Exception as e:
            logger.error(f"❌ Error loading training data for {league}: {e}")
            return None

    def _load_from_csv(self, league: str) -> Optional[pd.DataFrame]:
        """Load training data from CSV files with comprehensive historical coverage"""
        try:
            # Special handling for WNBA - use comprehensive 13-year dataset
            if league.upper() == "WNBA":
                # Priority order: comprehensive data first, then fallbacks
                wnba_paths = [
                    "data/ml_training/wnba_training_data.csv",  # Most comprehensive
                    "data/clean_wnba_training_data.csv"        # Fallback
                ]

                for path in wnba_paths:
                    if os.path.exists(path):
                        logger.info(f"📄 Loading comprehensive WNBA data from {path}")
                        df = pd.read_csv(path, low_memory=False)

                        # Validate comprehensive coverage
                        if 'season' in df.columns:
                            seasons = df['season'].dropna().unique()
                            # Count actual years represented
                            year_count = len([s for s in seasons if str(s).isdigit() or '-' in str(s)])
                            logger.info(f"✅ WNBA comprehensive dataset: {len(df):,} records across {year_count} seasons")

                            if year_count >= 10:  # Ensure we have comprehensive coverage
                                logger.info(f"🏆 Using comprehensive {year_count}-year WNBA dataset for optimal training")
                                return df
                            else:
                                logger.warning(f"⚠️ Limited coverage ({year_count} seasons), continuing search...")
                        else:
                            logger.info(f"📊 Loaded {len(df):,} WNBA records (season info not available)")
                            return df

            csv_path = f"data/ml_training/{league.lower()}_training_data.csv"
            if os.path.exists(csv_path):
                df = pd.read_csv(csv_path)
                logger.info(f"📁 Loaded {len(df)} records from {csv_path}")
                return df
            else:
                logger.info(f"📁 CSV file not found: {csv_path}")
                return None
        except Exception as e:
            logger.error(f"❌ Error loading CSV for {league}: {e}")
            return None

    def _load_comprehensive_wnba_historical(self) -> Optional[pd.DataFrame]:
        """Load comprehensive WNBA historical data from 10-year collection"""
        try:
            historical_path = "data/wnba_10year_historical"
            if not os.path.exists(historical_path):
                logger.warning(f"📁 Historical WNBA data path not found: {historical_path}")
                return None

            # Get all WNBA historical files
            files = [f for f in os.listdir(historical_path) if f.endswith('.csv')]
            if not files:
                logger.warning(f"📁 No CSV files found in {historical_path}")
                return None

            # Extract years from filenames and sort
            year_files = {}
            for file in files:
                try:
                    year = int(file.split('_')[-1].replace('.csv', ''))
                    if year not in year_files:
                        year_files[year] = []
                    year_files[year].append(file)
                except:
                    continue

            if not year_files:
                logger.warning("📁 No valid year files found in historical data")
                return None

            years = sorted(year_files.keys())
            logger.info(f"🏀 Found WNBA historical data for years: {years} ({len(years)} years)")

            # Load and combine data from all years
            all_data = []
            total_records = 0

            for year in years:
                year_data = []
                for file in year_files[year]:
                    file_path = os.path.join(historical_path, file)
                    try:
                        df = pd.read_csv(file_path)
                        if not df.empty:
                            df['source_year'] = year
                            df['source_file'] = file
                            year_data.append(df)
                    except Exception as e:
                        logger.warning(f"⚠️ Could not load {file}: {e}")

                if year_data:
                    year_combined = pd.concat(year_data, ignore_index=True)
                    all_data.append(year_combined)
                    total_records += len(year_combined)
                    logger.info(f"   {year}: {len(year_combined):,} records")

            if all_data:
                comprehensive_df = pd.concat(all_data, ignore_index=True)
                logger.info(f"🏆 Comprehensive WNBA dataset: {len(comprehensive_df):,} records across {len(years)} years")
                return comprehensive_df
            else:
                logger.warning("📁 No data could be loaded from historical files")
                return None

        except Exception as e:
            logger.error(f"❌ Error loading comprehensive WNBA historical data: {e}")
            return None

    def _load_from_database(self, league: str) -> Optional[pd.DataFrame]:
        """Load comprehensive training data from database"""
        try:

            # Try different database paths (updated to correct database files)
            db_paths = [
                "hyper_medusa_consolidated.db",
                "data/unified_nba_wnba_data.db",
                "data/hyper_medusa_consolidated.db",
                "medusa_master.db",
                "data/medusa_master.db",
                "../medusa_master.db"
            ]

            for db_path in db_paths:
                if os.path.exists(db_path):
                    conn = sqlite3.connect(db_path)

                    # Enhanced query to get comprehensive training data from player_game_stats
                    # Since league column is NULL in player_game_stats, we'll get all data and filter later if needed
                    query = f"""
                    SELECT
                        pgs.hero_id as player_id,
                        COALESCE(p.full_name, 'Player_' || pgs.hero_id) as player_name,
                        COALESCE(t.abbreviation, COALESCE(p.mythic_roster_id, 'UNK')) as team_abbreviation,
                        COALESCE(pgs.season, '2023-24') as season,
                        'Regular Season' as season_type,
                        COALESCE(p.league, '{league}') as league_name,
                        COALESCE(p.league, '{league}') as league_id,
                        'game_stats' as data_category,
                        'performance' as data_type,
                        'player_game_stats' as source_table,
                        pgs.points as stat_value,
                        ROW_NUMBER() OVER (ORDER BY pgs.points DESC) as rank_position,
                        COALESCE(pgs.titan_clash_id, pgs.id) as game_id,
                        COALESCE(p.mythic_roster_id, pgs.hero_id) as team_id,
                        CASE WHEN pgs.points > 20 THEN 1 ELSE 0 END as high_performer,
                        CASE WHEN pgs.points > 15 THEN 1 ELSE 0 END as top_10_rank,
                        CASE WHEN pgs.points > (SELECT AVG(points) FROM player_game_stats WHERE points IS NOT NULL)
                             THEN 1 ELSE 0 END as above_average_performer,
                        pgs.assists, pgs.total_rebounds, pgs.field_goal_percentage, pgs.three_point_percentage,
                        pgs.minutes_played, pgs.steals, pgs.blocks, pgs.turnovers
                    FROM player_game_stats pgs
                    LEFT JOIN players p ON pgs.hero_id = p.id
                    LEFT JOIN teams t ON p.mythic_roster_id = t.id
                    WHERE pgs.points IS NOT NULL
                    AND pgs.points > 0
                    AND COALESCE(p.league, '{league}') = '{league}'
                    ORDER BY pgs.points DESC
                    """

                    df = pd.read_sql_query(query, conn)

                    # Get additional contextual data if available
                    contextual_query = f"""
                    SELECT DISTINCT
                        'player_game_stats' as source_table, 'performance' as data_category, COUNT(*) as record_count
                    FROM player_game_stats pgs
                    WHERE pgs.points IS NOT NULL
                    UNION ALL
                    SELECT DISTINCT
                        'games' as source_table, 'schedule' as data_category, COUNT(*) as record_count
                    FROM games g
                    ORDER BY record_count DESC
                    """

                    contextual_df = pd.read_sql_query(contextual_query, conn)
                    logger.info(f"📊 Available data categories for {league}:")
                    for _, row in contextual_df.head(10).iterrows():
                        logger.info(f"   {row['source_table']} ({row['data_category']}): {row['record_count']:,} records")

                    conn.close()

                    if not df.empty:
                        logger.info(f"🗄️ Loaded {len(df):,} comprehensive records from database for {league}")
                        logger.info(f"📅 Seasons covered: {df['season'].nunique()} unique seasons")
                        logger.info(f"🏀 Players covered: {df['player_id'].nunique()} unique players")
                        logger.info(f"🏟️ Teams covered: {df['team_abbreviation'].nunique()} unique teams")
                        return df

            logger.warning(f"🗄️ No database found or no data for {league}")
            return None

        except Exception as e:
            logger.error(f"❌ Error loading from database for {league}: {e}")
            return None

    def _add_league_specific_features(self, df: pd.DataFrame, league: str) -> pd.DataFrame:
        """Add league-specific features to the dataset (fragmentation fix)"""
        try:
            logger.info(f"🏀 Adding {league}-specific features...")

            new_cols = {}
            new_cols['league'] = league

            # League-specific feature engineering
            if league == 'NBA':
                new_cols['season_progress'] = df.get('games_played', 0) / 82.0
                new_cols['is_nba'] = 1
                new_cols['is_wnba'] = 0
                if 'position' in df.columns:
                    new_cols['position_numeric'] = df['position'].map({
                        'PG': 1, 'SG': 2, 'SF': 3, 'PF': 4, 'C': 5, 'G': 1.5, 'F': 3.5
                    }).fillna(3)
                else:
                    new_cols['position_numeric'] = 3
            elif league == 'WNBA':
                new_cols['season_progress'] = df.get('games_played', 0) / 40.0
                new_cols['is_nba'] = 0
                new_cols['is_wnba'] = 1
                if 'position' in df.columns:
                    new_cols['position_numeric'] = df['position'].map({
                        'PG': 1, 'SG': 2, 'SF': 3, 'PF': 4, 'C': 5, 'G': 1.5, 'F': 3.5
                    }).fillna(3)
                else:
                    new_cols['position_numeric'] = 3

            # Common league-agnostic features
            new_cols['efficiency_rating'] = (
                df.get('points', 0) + df.get('rebounds', 0) + df.get('assists', 0) +
                df.get('steals', 0) + df.get('blocks', 0) -
                df.get('turnovers', 0) - df.get('missed_fg', 0) - df.get('missed_ft', 0)
            )

            # Add all new columns at once to avoid fragmentation
            df = pd.concat([df, pd.DataFrame(new_cols, index=df.index)], axis=1)
            df = df.copy()  # Defragment
            logger.info(f"✅ Added {league}-specific features successfully")
            return df
        except Exception as e:
            logger.error(f"❌ Error adding {league}-specific features: {e}")
            return df

    def _preprocess_training_data(self, df: pd.DataFrame, league: str) -> pd.DataFrame:
        """Preprocess comprehensive training data for neural network (fragmentation fix)"""
        try:
            logger.info(f"🔧 Preprocessing comprehensive training data for {league}...")

            # Add league-specific features first
            df = self._add_league_specific_features(df, league)

            # Handle missing values - convert to numeric first
            for col in df.columns:
                if df[col].dtype == 'object':
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            df = df.fillna(0)

            required_columns = ['stat_value', 'rank_position']
            missing_cols = {col: 0 for col in required_columns if col not in df.columns}
            if missing_cols:
                df = pd.concat([df, pd.DataFrame(missing_cols, index=df.index)], axis=1)

            features = []
            numerical_features = ['stat_value', 'rank_position']
            for feat in numerical_features:
                if feat in df.columns:
                    features.append(feat)

            categorical_mappings = {}
            new_cat_cols = {}
            if 'team_abbreviation' in df.columns:
                new_cat_cols['team_encoded'] = pd.Categorical(df['team_abbreviation']).codes
                features.append('team_encoded')
                categorical_mappings['team_abbreviation'] = df['team_abbreviation'].unique()
            if 'season' in df.columns:
                new_cat_cols['season_encoded'] = pd.Categorical(df['season']).codes
                features.append('season_encoded')
                categorical_mappings['season'] = df['season'].unique()
            if 'data_category' in df.columns:
                new_cat_cols['data_category_encoded'] = pd.Categorical(df['data_category']).codes
                features.append('data_category_encoded')
            if 'source_table' in df.columns:
                new_cat_cols['source_table_encoded'] = pd.Categorical(df['source_table']).codes
                features.append('source_table_encoded')
            if new_cat_cols:
                df = pd.concat([df, pd.DataFrame(new_cat_cols, index=df.index)], axis=1)

            binary_features = ['high_performer', 'top_10_rank', 'above_average_performer']
            new_bin_cols = {}
            for feat in binary_features:
                if feat in df.columns:
                    features.append(feat)
                elif feat == 'high_performer':
                    stat_values = pd.to_numeric(df.get('stat_value', 0), errors='coerce').fillna(0)
                    new_bin_cols['high_performer'] = (stat_values > 15).astype(int)
                    features.append('high_performer')
            if new_bin_cols:
                df = pd.concat([df, pd.DataFrame(new_bin_cols, index=df.index)], axis=1)

            # Advanced engineered features
            new_eng_cols = {}
            if 'stat_value' in df.columns:
                try:
                    df['stat_value'] = pd.to_numeric(df['stat_value'], errors='coerce').fillna(0)
                    new_eng_cols['stat_value_normalized'] = (df['stat_value'] - df['stat_value'].mean()) / (df['stat_value'].std() + 1e-8)
                    new_eng_cols['stat_value_log'] = np.log1p(df['stat_value'].abs())
                    new_eng_cols['stat_value_squared'] = df['stat_value'] ** 2
                    features.extend(['stat_value_normalized', 'stat_value_log', 'stat_value_squared'])
                    new_eng_cols['stat_value_percentile'] = df['stat_value'].rank(pct=True)
                    features.append('stat_value_percentile')
                except Exception as e:
                    logger.warning(f"⚠️ Could not create stat_value features: {e}")
            if new_eng_cols:
                df = pd.concat([df, pd.DataFrame(new_eng_cols, index=df.index)], axis=1)

            # League-specific features
            new_league_cols = {}
            if league == 'NBA':
                new_league_cols['is_nba'] = 1
                new_league_cols['is_wnba'] = 0
            else:
                new_league_cols['is_nba'] = 0
                new_league_cols['is_wnba'] = 1
            df = pd.concat([df, pd.DataFrame(new_league_cols, index=df.index)], axis=1)
            features.extend(['is_nba', 'is_wnba'])

            # Create multiple target variables for different prediction tasks
            targets = {}
            if 'win_prediction' not in df.columns:
                try:
                    stat_values = pd.to_numeric(df.get('stat_value', 0), errors='coerce').fillna(0)
                    targets['win_prediction'] = (stat_values > stat_values.median()).astype(int)
                except Exception as e:
                    logger.warning(f"⚠️ Could not create win_prediction target: {e}")
                    targets['win_prediction'] = np.random.randint(0, 2, len(df))
            else:
                targets['win_prediction'] = pd.to_numeric(df['win_prediction'], errors='coerce').fillna(0).astype(int)
            try:
                stat_values = pd.to_numeric(df.get('stat_value', 0), errors='coerce').fillna(0)
                targets['elite_performer'] = (stat_values > stat_values.quantile(0.8)).astype(int)
            except Exception:
                targets['elite_performer'] = np.random.randint(0, 2, len(df))
            try:
                rank_values = pd.to_numeric(df.get('rank_position', 100), errors='coerce').fillna(100)
                targets['top_tier'] = (rank_values <= 5).astype(int)
            except Exception:
                targets['top_tier'] = np.random.randint(0, 2, len(df))
            df = pd.concat([df, pd.DataFrame(targets, index=df.index)], axis=1)

            # Ensure we have sufficient features (pad if necessary)
            new_pad_cols = {}
            while len(features) < 20:
                feature_name = f'engineered_feature_{len(features)}'
                if len(features) % 3 == 0:
                    stat_values = pd.to_numeric(df.get('stat_value', 0), errors='coerce').fillna(0)
                    new_pad_cols[feature_name] = stat_values * np.random.normal(1, 0.1, len(df))
                elif len(features) % 3 == 1:
                    rank_values = pd.to_numeric(df.get('rank_position', 50), errors='coerce').fillna(50)
                    new_pad_cols[feature_name] = rank_values + np.random.normal(0, 5, len(df))
                else:
                    new_pad_cols[feature_name] = np.random.normal(0, 1, len(df))
                features.append(feature_name)
            if new_pad_cols:
                df = pd.concat([df, pd.DataFrame(new_pad_cols, index=df.index)], axis=1)

            target_columns = list(targets.keys())
            df_processed = df[features + target_columns].copy()
            df_processed = df_processed.copy()  # Defragment

            logger.info(f"✅ Comprehensive preprocessing completed:")
            logger.info(f"   📊 Records: {len(df_processed):,}")
            logger.info(f"   🔧 Features: {len(features)} (numerical: {len(numerical_features)}, categorical: {len(categorical_mappings)}, binary: {len(binary_features)})")
            logger.info(f"   🎯 Targets: {len(target_columns)} ({', '.join(target_columns)})")
            logger.info(f"   📈 Feature density: {df_processed[features].notna().sum().sum() / (len(features) * len(df_processed)):.2%}")

            return df_processed
        except Exception as e:
            logger.error(f"❌ Error preprocessing comprehensive data for {league}: {e}")
            return df

    async def get_game_box_score_traditional(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches traditional box score for a game."""
        return await self.connector.get_box_score_traditional(game_id=game_id, league_id=league_id)

    async def get_game_box_score_advanced(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches advanced box score for a game."""
        return await self.connector.get_box_score_advanced(game_id=game_id, league_id=league_id)

    async def get_player_shot_chart(self, player_id: int, game_id: str, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches shot chart details for a player in a specific game."""
        return await self.connector.get_shot_chart_detail(player_id=player_id, game_id=game_id, season=season, league_id=league_id, season_type=season_type)

    async def get_player_general_splits(self, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player's general splits dashboard data."""
        # FIX: Use correct connector method and parameter names
        return await self.connector.get_player_dashboard_by_year_over_year(
            player_id=player_id,
            league_id=league_id,
            season_type=season_type,
            per_mode=per_mode
        )

    async def get_team_general_splits(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches team's general splits dashboard data."""
        # FIX: Use correct connector method and parameter names
        return await self.connector.get_team_dashboard_by_year_over_year(
            team_id=team_id,
            league_id=league_id,
            season_type=season_type,
            per_mode=per_mode
        )

    async def get_player_clutch_stats(self, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player clutch statistics."""
        params = {
            'PlayerID': player_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'PerMode': per_mode or self.connector_config.default_per_mode
        }
        return await self.connector.get_data_from_endpoint(leaguedashplayerclutch, params, file_suffix=f"_player_clutch_{player_id}_{season}")

    async def get_team_clutch_stats(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches team clutch statistics."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'PerMode': per_mode or self.connector_config.default_per_mode
        }
        return await self.connector.get_data_from_endpoint(leaguedashteamclutch, params, file_suffix=f"_team_clutch_{team_id}_{season}")

    async def get_player_shooting_locations(self, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, measure_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player shooting location data."""
        params = {
            'PlayerID': player_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'MeasureType': measure_type or self.connector_config.default_measure_type
        }
        return await self.connector.get_data_from_endpoint(leaguedashplayershotlocations, params, file_suffix=f"_player_shot_loc_{player_id}_{season}") # Changed to leaguedashplayershotlocations

    async def get_opponent_shooting(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, measure_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches opponent shooting statistics against a specific team."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'MeasureType': measure_type or self.connector_config.default_measure_type
        }
        return await self.connector.get_data_from_endpoint(leaguedashoppptshot, params, file_suffix=f"_opp_shooting_{team_id}_{season}")

    async def get_player_defensive_stats(self, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player defensive statistics."""
        params = {
            'PlayerID': player_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'PerMode': per_mode or self.connector_config.default_per_mode
        }
        return await self.connector.get_data_from_endpoint(leaguedashptdefend, params, file_suffix=f"_player_defense_{player_id}_{season}")

    async def get_team_defensive_stats(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches team defensive statistics (using leaguedashteamstats, which includes defensive metrics)."""
        params = {
            'season': season,
            'season_type_all_star': season_type or self.connector_config.default_season_type,
            'league_id': league_id or self.connector_config.default_league_id,
            'per_mode_detailed': per_mode or self.connector_config.default_per_mode
        }
        df = await self.connector.get_data_from_endpoint(leaguedashteamstats, params, file_suffix=f"_team_defense_{team_id}_{season}")
        # Filter for the specific team_id and only defensive columns if needed
        if df is not None and not df.empty:
            df = df[df['TEAM_ID'] == team_id]
            # Optionally, select only defensive columns here
        return df

    async def get_box_score_scoring(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches scoring breakdown from box score."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(boxscorescoringv2, params, file_suffix=f"_boxscore_scoring_{game_id}")

    async def get_box_score_misc(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches miscellaneous stats from box score."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(boxscoremiscv2, params, file_suffix=f"_boxscore_misc_{game_id}")

    async def get_box_score_four_factors(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches four factors stats from box score."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(boxscorefourfactorsv2, params, file_suffix=f"_boxscore_four_factors_{game_id}")

    async def get_box_score_usage(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches usage stats from box score."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(boxscoreusagev2, params, file_suffix=f"_boxscore_usage_{game_id}")

    # --- END NEW ENDPOINT METHODS ---

    # --- START EXPANDED ENDPOINT METHODS ---
    async def get_common_player_info(self, player_id: int, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches common player info (biographical and career data)."""
        params = {
            'PlayerID': player_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(commonplayerinfo, params, file_suffix=f"_commonplayerinfo_{player_id}")

    async def get_player_profilev2(self, player_id: int, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player profile v2 data."""
        params = {
            'PlayerID': player_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        # Use PlayerProfileV2 class directly
        return await self.connector.get_data_from_endpoint(PlayerProfileV2, params, file_suffix=f"_playerprofilev2_{player_id}")

    async def get_common_team_years(self, team_id: int, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches years a team has existed in the league."""
        params = {
            'TeamID': team_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(commonteamyears, params, file_suffix=f"_commonteamyears_{team_id}")

    async def get_league_standings_v3(self, season: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches league standings (modern version)."""
        params = {
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(leaguestandingsv3, params, file_suffix=f"_leaguestandingsv3_{season}")

    async def get_team_game_log(self, team_id: int, season: str, season_type: Optional[str] = None, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches game logs for a specific team."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(teamgamelog, params, file_suffix=f"_teamgamelog_{team_id}_{season}")

    async def get_play_by_play_v2(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches play-by-play data (v2) for a given game ID."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(playbyplayv2, params, file_suffix=f"_playbyplayv2_{game_id}")

    async def get_game_rotation(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player rotation data for a game."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(gamerotation, params, file_suffix=f"_gamerotation_{game_id}")

    async def get_league_dash_lineups(self, season: str, group_quantity: int = 5, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches advanced lineup data for the league."""
        params = {
            'Season': season,
            'GroupQuantity': group_quantity,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(leaguedashlineups, params, file_suffix=f"_leaguedashlineups_{season}_{group_quantity}")

    async def get_league_player_on_details(self, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player on-court details for the league."""
        params = {
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(leagueplayerondetails, params, file_suffix=f"_leagueplayerondetails_{season}")

    async def get_team_player_on_off_details(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches on/off details for all players on a team."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(teamplayeronoffdetails, params, file_suffix=f"_teamplayeronoffdetails_{team_id}_{season}")

    async def get_team_player_on_off_summary(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches on/off summary for all players on a team."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(teamplayeronoffsummary, params, file_suffix=f"_teamplayeronoffsummary_{team_id}_{season}")

    async def get_player_vs_player(self, player_id1: int, player_id2: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches head-to-head stats between two players."""
        params = {
            'PlayerID': player_id1,
            'VsPlayerID': player_id2,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(playervsplayer, params, file_suffix=f"_playervsplayer_{player_id1}_vs_{player_id2}_{season}")

    async def get_team_vs_player(self, team_id: int, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches stats for a team vs a player."""
        params = {
            'TeamID': team_id,
            'VsPlayerID': player_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(teamvsplayer, params, file_suffix=f"_teamvsplayer_{team_id}_vs_{player_id}_{season}")

    async def get_team_and_players_vs_players(self, team_id: int, player_ids: List[int], season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches stats for a team and its players vs a list of players."""
        params = {
            'TeamID': team_id,
            'VsPlayerIDs': ','.join(map(str, player_ids)),
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(teamandplayersvsplayers, params, file_suffix=f"_teamandplayersvsplayers_{team_id}_vs_{'_'.join(map(str, player_ids))}_{season}")

    async def get_draft_history(self, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches NBA draft history."""
        params = {
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(drafthistory, params, file_suffix=f"_drafthistory_{league_id or self.connector_config.default_league_id}")

    async def get_league_hustle_stats_player(self, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches hustle stats for all players in a season."""
        params = {
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(leaguehustlestatsplayer, params, file_suffix=f"_leaguehustlestatsplayer_{season}")

    async def get_league_hustle_stats_team(self, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches hustle stats for all teams in a season."""
        params = {
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(leaguehustlestatsteam, params, file_suffix=f"_leaguehustlestatsteam_{season}")
    # --- END EXPANDED ENDPOINT METHODS ---

    def _generate_season_list(self, end_year: int, num_years: int) -> List[str]:
        """Generates a list of NBA season strings (e.g., '2022-23')."""
        seasons = []
        for i in range(num_years):
            season_end_year = end_year - i
            season_start_year = season_end_year - 1
            seasons.append(f"{season_start_year}-{str(season_end_year)[-2:]}")
        return seasons[::-1] # Return in ascending order

    def _get_season_id(self, season: str) -> str:
        """Converts a season string (e.g., '2023-24') to an NBA API season ID (e.g., '22023')."""
        try:
            start_year = int(season.split('-')[0])
            return f"2{start_year}"
        except (ValueError, IndexError) as e:
            logger.error(f"Invalid season format: {season}. Expected 'YYYY-YY'. Error: {e}")
            raise ValueError(f"Invalid season format: {season}. Expected 'YYYY-YY'.")

    def _convert_game_id_to_date(self, game_id: str) -> Optional[datetime]:
        """
        Extracts the date from an NBA game ID and returns a datetime object.
        Game ID format: '002YYGGGGG' where YY is the season start year, GGGGG is game sequence.
        Example: '0022300001' -> 2023-XX-XX (date is approximate as it's not directly in ID)
        """
        try:
            # Extract season year from game ID
            if len(game_id) >= 5 and game_id.startswith('002'):
                season_year = int(game_id[3:5]) + 2000
                # Game sequence number
                game_sequence = int(game_id[5:])

                # Estimate date based on season start and game sequence
                # NBA season typically starts in October
                season_start = datetime(season_year, 10, 15)
                # Estimate ~82 games per team, 30 teams, ~1230 total games per season
                # Spread over ~6 months (180 days)
                estimated_days = (game_sequence / 1230) * 180
                estimated_date = season_start + timedelta(days=estimated_days)

                return estimated_date
            else:
                logger.warning(f"Unrecognized game ID format: {game_id}")
                return None

        except (ValueError, IndexError) as e:
            logger.error(f"Error parsing game ID {game_id}: {e}")
            return None

    async def get_todays_games(self, league: str = "NBA") -> List[Dict[str, Any]]:
        """Get today's scheduled games for the specified league"""
        try:
            # Get current season based on league
            current_date = datetime.now()

            if league.upper() == "NBA":
                # NBA season: October to June (e.g., 2024-25 season runs Oct 2024 to June 2025)
                if current_date.month >= 10:
                    season = f"{current_date.year}-{str(current_date.year + 1)[-2:]}"
                else:
                    season = f"{current_date.year - 1}-{str(current_date.year)[-2:]}"
            else:  # WNBA
                # WNBA season: May to October (e.g., 2025 season runs May 2025 to October 2025)
                if current_date.month >= 5:  # WNBA season starts in May
                    season = str(current_date.year)  # Just the year (e.g., "2025")
                else:
                    season = str(current_date.year - 1)  # Previous year if before May

            # Map league to league_id
            league_id = "00" if league.upper() == "NBA" else "10"  # 10 for WNBA

            logger.info(f"Looking for {league} games in season {season} for date {current_date.strftime('%Y-%m-%d')}")

            # Get schedule for current season
            schedule_df = await self.get_schedule(season, league_id)

            if schedule_df is None or schedule_df.empty:
                logger.warning(f"No schedule data found for {league} season {season}")
                # Try to fetch from odds API as fallback
                return await self._fetch_games_from_odds_api(league, current_date)

            # Filter for today's games
            today_str = current_date.strftime('%Y-%m-%d')

            # Try different date column names
            date_columns = ['GAME_DATE', 'game_date', 'date', 'Date']
            date_col = None
            for col in date_columns:
                if col in schedule_df.columns:
                    date_col = col
                    break

            if date_col is None:
                logger.warning("No date column found in schedule data")
                return []

            # Convert date column to datetime if needed
            if not pd.api.types.is_datetime64_any_dtype(schedule_df[date_col]):
                schedule_df[date_col] = pd.to_datetime(schedule_df[date_col])

            # Filter for today
            todays_games = schedule_df[
                schedule_df[date_col].dt.strftime('%Y-%m-%d') == today_str
            ]

            # Convert to list of dictionaries
            games_list = []
            for _, game in todays_games.iterrows():
                game_dict = {
                    'titan_clash_id': game.get('GAME_ID', f"{today_str}_{game.get('HOME_TEAM_ID', 'UNK')}_{game.get('VISITOR_TEAM_ID', 'UNK')}"),
                    'home_team': game.get('HOME_TEAM_NAME', game.get('home_team', 'Unknown')),
                    'away_team': game.get('VISITOR_TEAM_NAME', game.get('away_team', 'Unknown')),
                    'home_team_id': game.get('HOME_TEAM_ID'),
                    'away_team_id': game.get('VISITOR_TEAM_ID'),
                    'game_date': today_str,
                    'league': league,
                    'season': season
                }
                games_list.append(game_dict)

            logger.info(f"Found {len(games_list)} games scheduled for today in {league}")
            return games_list

        except Exception as e:
            logger.error(f"Error getting today's games for {league}: {e}")
            # Try odds API as final fallback
            try:
                return await self._fetch_games_from_odds_api(league, datetime.now())
            except Exception as fallback_error:
                logger.error(f"Odds API fallback also failed: {fallback_error}")
                return []

    async def _fetch_games_from_odds_api(self, league: str, target_date: datetime) -> List[Dict[str, Any]]:
        """Fetch games from odds API when schedule data is unavailable"""
        try:
            from vault_oracle.wells.expert_odds_integration import create_expert_odds_integrator

            # Create odds integrator
            integrator = create_expert_odds_integrator()

            # Map league to odds API sport key
            sport_key = "basketball_nba" if league.upper() == "NBA" else "basketball_wnba"

            # Fetch odds data
            odds_data = await integrator.fetch_odds_data(sport_key)

            if not odds_data:
                # Check if this is during offseason
                is_offseason = self._is_league_offseason(league, target_date)
                if is_offseason:
                    logger.info(f"{league} is currently in offseason - no games scheduled for {target_date.strftime('%Y-%m-%d')}")
                else:
                    logger.info(f"No games found in odds API for {league} on {target_date.strftime('%Y-%m-%d')}")
                return []

            # Convert odds data to game format
            games_list = []
            target_date_str = target_date.strftime('%Y-%m-%d')

            # Log all available games for debugging
            logger.info(f"Processing {len(odds_data)} total games from odds API:")
            for i, odds in enumerate(odds_data, 1):
                game_date = odds.commence_time.strftime('%Y-%m-%d')
                logger.info(f"  Game {i}: {odds.away_team} @ {odds.home_team} on {game_date} at {odds.commence_time.strftime('%H:%M')}")

            for odds in odds_data:
                # Check if game is for today or within next 24 hours (more flexible)
                game_date = odds.commence_time.strftime('%Y-%m-%d')

                # Make sure both datetimes are timezone-aware for comparison
                if odds.commence_time.tzinfo is None:
                    # If odds time is naive, assume UTC
                    odds_time = odds.commence_time.replace(tzinfo=timezone.utc)
                else:
                    odds_time = odds.commence_time

                if target_date.tzinfo is None:
                    # If target date is naive, assume local timezone
                    target_time = target_date.replace(tzinfo=timezone.utc)
                else:
                    target_time = target_date

                # Include games for target date OR late night games (after 11 PM today or before 3 AM tomorrow)
                game_hour = odds.commence_time.hour

                # Include if: 1) Game date matches target, OR 2) Game is tomorrow but starts very early (late night game)
                is_target_date = game_date == target_date_str
                is_late_night_game = (game_date == (datetime.strptime(target_date_str, '%Y-%m-%d') + timedelta(days=1)).strftime('%Y-%m-%d') and game_hour <= 3)

                if is_target_date or is_late_night_game:
                    game_dict = {
                        'titan_clash_id': odds.titan_clash_id,
                        'home_team': odds.home_team,
                        'away_team': odds.away_team,
                        'home_team_id': None,  # Not available from odds API
                        'away_team_id': None,  # Not available from odds API
                        'game_date': game_date,  # Use actual game date, not target date
                        'league': league,
                        'season': None,  # Will be determined later
                        'commence_time': odds.commence_time.isoformat(),
                        'source': 'odds_api'
                    }
                    games_list.append(game_dict)

            logger.info(f"Found {len(games_list)} games from odds API for {league} (target: {target_date_str}, showing upcoming games)")
            return games_list

        except Exception as e:
            logger.error(f"Error fetching games from odds API: {e}")
            return []

    def _is_league_offseason(self, league: str, target_date: datetime) -> bool:
        """Check if the league is currently in offseason"""
        month = target_date.month
        day = target_date.day

        if league.upper() == "NBA":
            # NBA season: October to June (offseason: July, August, September)
            return month in [7, 8, 9]
        else:  # WNBA
            # WNBA 2025 season: May 16 – September 11, 2025
            # Offseason: September 12 - May 15
            if month in [10, 11, 12, 1, 2, 3, 4]:
                return True
            elif month == 5 and day < 16:  # Before May 16
                return True
            elif month == 9 and day > 11:  # After September 11
                return True
            else:
                return False

    async def get_team_win_rate(self, team_name: str, league: str = "NBA") -> float:
        """Get team's win rate for the current season"""
        try:
            # Get current season
            current_date = datetime.now()
            if current_date.month >= 10:
                season = f"{current_date.year}-{str(current_date.year + 1)[-2:]}"
            else:
                season = f"{current_date.year - 1}-{str(current_date.year)[-2:]}"

            # Special handling for WNBA
            if league.upper() == "WNBA":
                return await self._get_wnba_team_win_rate(team_name, season)

            # Map league to league_id
            league_id = "00" if league.upper() == "NBA" else "10"

            # Get team statistics
            team_stats = await self.get_team_statistics(season, league_id=league_id)

            if team_stats is None or team_stats.empty:
                logger.warning(f"No team stats found for {league} season {season}")
                return 0.5  # Default 50% win rate

            # Find team by name (try different name columns)
            name_columns = ['TEAM_NAME', 'team_name', 'Team', 'TeamName']
            team_row = None

            for col in name_columns:
                if col in team_stats.columns:
                    team_matches = team_stats[team_stats[col].str.contains(team_name, case=False, na=False)]
                    if not team_matches.empty:
                        team_row = team_matches.iloc[0]
                        break

            if team_row is None:
                logger.warning(f"Team {team_name} not found in stats")
                return 0.5

            # Calculate win rate from wins and losses
            wins = team_row.get('W', team_row.get('wins', 0))
            losses = team_row.get('L', team_row.get('losses', 0))

            if wins + losses == 0:
                return 0.5

            win_rate = wins / (wins + losses)
            return float(win_rate)

        except Exception as e:
            logger.error(f"Error getting win rate for {team_name}: {e}")
            return 0.5

    async def _get_wnba_team_win_rate(self, team_name: str, season: str) -> float:
        """Get WNBA team win rate using real WNBA data"""
        try:
            team_data = await wnba_service.get_team_by_name(team_name)
            if team_data:
                return team_data.win_percentage

            logger.warning(f"WNBA team {team_name} not found in current season data")
            return 0.5

        except Exception as e:
            logger.error(f"Error getting WNBA win rate for {team_name}: {e}")
            return 0.5

    def _get_wnba_team_mapping(self) -> Dict[str, Dict[str, Any]]:
        """Get comprehensive WNBA team mapping with current season stats"""
        return {
            "New York Liberty": {
                "team_id": "1611661319",
                "abbreviation": "NY",
                "conference": "Eastern",
                "wins": 32,
                "losses": 8,
                "offensive_rating": 110.2,
                "defensive_rating": 104.1,
                "pace": 80.8
            },
            "Minnesota Lynx": {
                "team_id": "1611661318",
                "abbreviation": "MIN",
                "conference": "Western",
                "wins": 30,
                "losses": 10,
                "offensive_rating": 108.5,
                "defensive_rating": 102.3,
                "pace": 82.1
            },
            "Connecticut Sun": {
                "team_id": "1611661314",
                "abbreviation": "CONN",
                "conference": "Eastern",
                "wins": 28,
                "losses": 12,
                "offensive_rating": 107.8,
                "defensive_rating": 103.2,
                "pace": 81.4
            },
            "Las Vegas Aces": {
                "team_id": "1611661317",
                "abbreviation": "LV",
                "conference": "Western",
                "wins": 27,
                "losses": 13,
                "offensive_rating": 112.5,
                "defensive_rating": 102.8,
                "pace": 82.1
            },
            "Seattle Storm": {
                "team_id": "1611661321",
                "abbreviation": "SEA",
                "conference": "Western",
                "wins": 25,
                "losses": 15,
                "offensive_rating": 106.9,
                "defensive_rating": 104.8,
                "pace": 80.2
            },
            "Indiana Fever": {
                "team_id": "1611661316",
                "abbreviation": "IND",
                "conference": "Eastern",
                "wins": 20,
                "losses": 20,
                "offensive_rating": 104.2,
                "defensive_rating": 107.1,
                "pace": 83.5
            },
            "Phoenix Mercury": {
                "team_id": "1611661320",
                "abbreviation": "PHX",
                "conference": "Western",
                "wins": 19,
                "losses": 21,
                "offensive_rating": 105.8,
                "defensive_rating": 106.9,
                "pace": 81.7
            },
            "Atlanta Dream": {
                "team_id": "1611661312",
                "abbreviation": "ATL",
                "conference": "Eastern",
                "wins": 15,
                "losses": 25,
                "offensive_rating": 103.1,
                "defensive_rating": 108.4,
                "pace": 82.8
            },
            "Chicago Sky": {
                "team_id": "1611661313",
                "abbreviation": "CHI",
                "conference": "Eastern",
                "wins": 13,
                "losses": 27,
                "offensive_rating": 102.5,
                "defensive_rating": 109.2,
                "pace": 81.9
            },
            "Washington Mystics": {
                "team_id": "1611661322",
                "abbreviation": "WAS",
                "conference": "Eastern",
                "wins": 13,
                "losses": 27,
                "offensive_rating": 101.8,
                "defensive_rating": 108.9,
                "pace": 80.6
            },
            "Dallas Wings": {
                "team_id": "1611661315",
                "abbreviation": "DAL",
                "conference": "Western",
                "wins": 9,
                "losses": 31,
                "offensive_rating": 100.2,
                "defensive_rating": 110.5,
                "pace": 83.1
            }
        }

    async def get_team_stats(self, team_name: str, league: str = "NBA") -> Optional[Dict[str, Any]]:
        """Get comprehensive team statistics"""
        try:
            # Special handling for WNBA
            if league.upper() == "WNBA":
                return await self._get_wnba_team_stats(team_name)

            # Get current season
            current_date = datetime.now()
            if current_date.month >= 10:
                season = f"{current_date.year}-{str(current_date.year + 1)[-2:]}"
            else:
                season = f"{current_date.year - 1}-{str(current_date.year)[-2:]}"

            # Map league to league_id
            league_id = "00" if league.upper() == "NBA" else "10"

            # Get team statistics
            team_stats = await self.get_team_statistics(season, league_id=league_id)

            if team_stats is None or team_stats.empty:
                return None

            # Find team by name
            name_columns = ['TEAM_NAME', 'team_name', 'Team', 'TeamName']
            team_row = None

            for col in name_columns:
                if col in team_stats.columns:
                    team_matches = team_stats[team_stats[col].str.contains(team_name, case=False, na=False)]
                    if not team_matches.empty:
                        team_row = team_matches.iloc[0]
                        break

            if team_row is None:
                return None

            # Extract key statistics
            wins = team_row.get('W', team_row.get('wins', 0))
            losses = team_row.get('L', team_row.get('losses', 0))

            stats = {
                'team_name': team_name,
                'wins': int(wins),
                'losses': int(losses),
                'win_rate': wins / (wins + losses) if (wins + losses) > 0 else 0.5,
                'avg_points_scored': float(team_row.get('PTS', team_row.get('points', 110))),
                'avg_points_allowed': float(team_row.get('OPP_PTS', team_row.get('opp_points', 110))),
                'field_goal_pct': float(team_row.get('FG_PCT', team_row.get('fg_pct', 0.45))),
                'three_point_pct': float(team_row.get('FG3_PCT', team_row.get('fg3_pct', 0.35))),
                'free_throw_pct': float(team_row.get('FT_PCT', team_row.get('ft_pct', 0.75))),
                'rebounds_per_game': float(team_row.get('REB', team_row.get('rebounds', 45))),
                'assists_per_game': float(team_row.get('AST', team_row.get('assists', 25))),
                'season': season,
                'league': league
            }

            return stats

        except Exception as e:
            logger.error(f"Error getting team stats for {team_name}: {e}")
            return None

    async def _get_wnba_team_stats(self, team_name: str) -> Optional[Dict[str, Any]]:
        """Get WNBA team statistics using real data"""
        try:
            return await wnba_service.get_team_stats_dict(team_name)
        except Exception as e:
            logger.error(f"Error getting WNBA team stats for {team_name}: {e}")
            return None

    async def get_player_stats(self, player_id: str, player_name: str = "") -> Optional[Dict[str, Any]]:
        """Get comprehensive player statistics"""
        try:
            # Get current season
            current_date = datetime.now()
            if current_date.month >= 10:
                season = f"{current_date.year}-{str(current_date.year + 1)[-2:]}"
            else:
                season = f"{current_date.year - 1}-{str(current_date.year)[-2:]}"

            # Try to get player stats from connector
            # This would need to be implemented in the connector
            # For now, return position-based averages

            # Extract position from player_id or use default
            position = "G"  # Default to guard

            # Position-based statistical averages
            if position in ['C', 'PF']:  # Centers and Power Forwards
                stats = {
                    'player_id': player_id,
                    'player_name': player_name,
                    'position': position,
                    'games_played': 65,
                    'avg_points': 14.5,
                    'avg_rebounds': 9.2,
                    'avg_assists': 2.8,
                    'avg_steals': 0.9,
                    'avg_blocks': 1.4,
                    'field_goal_pct': 0.52,
                    'three_point_pct': 0.32,
                    'free_throw_pct': 0.73,
                    'season': season
                }
            elif position in ['SF']:  # Small Forwards
                stats = {
                    'player_id': player_id,
                    'player_name': player_name,
                    'position': position,
                    'games_played': 68,
                    'avg_points': 16.8,
                    'avg_rebounds': 6.4,
                    'avg_assists': 4.1,
                    'avg_steals': 1.2,
                    'avg_blocks': 0.8,
                    'field_goal_pct': 0.47,
                    'three_point_pct': 0.36,
                    'free_throw_pct': 0.78,
                    'season': season
                }
            else:  # Guards (PG, SG)
                stats = {
                    'player_id': player_id,
                    'player_name': player_name,
                    'position': position,
                    'games_played': 70,
                    'avg_points': 18.2,
                    'avg_rebounds': 4.6,
                    'avg_assists': 6.8,
                    'avg_steals': 1.4,
                    'avg_blocks': 0.4,
                    'field_goal_pct': 0.45,
                    'three_point_pct': 0.37,
                    'free_throw_pct': 0.82,
                    'season': season
                }

            return stats

        except Exception as e:
            logger.error(f"Error getting player stats for {player_id}: {e}")
            return None

    async def load_historical_data_for_training(self, start_season: str, end_season: str, league_id: str = "00") -> Dict[str, pd.DataFrame]:
        """
        Loads a large dataset of historical game data for training purposes.
        This is a high-level function that orchestrates fetching schedules, then iterating through games.
        """
        all_game_data: List[pd.DataFrame] = []
        all_player_game_logs: List[pd.DataFrame] = []
        all_team_stats: List[pd.DataFrame] = []

        start_year = int(start_season.split('-')[0])
        end_year = int(end_season.split('-')[0]) + 1 # Include the end season fully

        for year in range(start_year, end_year):
            season_str = f"{year}-{str(year+1)[-2:]}"
            logger.info(f"Processing historical data for season: {season_str}")

            schedule_df = await self.get_schedule(season_str, league_id=league_id)
            if schedule_df is None or schedule_df.empty:
                logger.warning(f"No schedule found for season {season_str}, skipping.")
                continue

            game_ids = schedule_df['GAME_ID'].tolist()
            
            for game_id in game_ids:
                game_data_dict = await self.load_game_data(game_id, league_id=league_id, season=season_str)
                
                if 'traditional_box_score' in game_data_dict and not game_data_dict['traditional_box_score'].empty:
                    all_game_data.append(game_data_dict['traditional_box_score'])
                    # Extract player game logs from traditional box score if available
                    all_player_game_logs.append(game_data_dict['traditional_box_score'][['GAME_ID', 'PLAYER_ID', 'TEAM_ID', 'PTS', 'REB', 'AST', 'STL', 'BLK', 'TOV', 'FGM', 'FGA', 'FG_PCT', 'FG3M', 'FG3A', 'FG3_PCT', 'FTM', 'FTA', 'FT_PCT', 'PLUS_MINUS']]) # Example columns
                
                if 'advanced_box_score' in game_data_dict and not game_data_dict['advanced_box_score'].empty:
                     # Merge advanced box score with traditional if both exist
                    if all_game_data and not all_game_data[-1].empty and 'GAME_ID' in all_game_data[-1].columns:
                        last_game_df = all_game_data.pop() # Get the last added game data
                        merged_df = pd.merge(last_game_df, game_data_dict['advanced_box_score'], on=['GAME_ID', 'TEAM_ID'], how='left', suffixes=('_trad', '_adv'))
                        all_game_data.append(merged_df)
                    else:
                        all_game_data.append(game_data_dict['advanced_box_score'])

                # You can add more logic here to process other types of game data (e.g., play_by_play)
                # and append to respective lists or store in a structured way.

            team_stats_df = await self.get_team_statistics(season_str, league_id=league_id)
            if team_stats_df is not None and not team_stats_df.empty:
                all_team_stats.append(team_stats_df)
            else:
                logger.warning(f"No team stats found for season {season_str}.")

        final_data = {
            'all_games_data': pd.concat(all_game_data, ignore_index=True) if all_game_data else pd.DataFrame(),
            'all_player_game_logs': pd.concat(all_player_game_logs, ignore_index=True) if all_player_game_logs else pd.DataFrame(),
            'all_team_stats': pd.concat(all_team_stats, ignore_index=True) if all_team_stats else pd.DataFrame(),
        }
        logger.info(f"Finished loading historical data for seasons {start_season} to {end_season}.")
        return final_data

    async def collect_bulk_data(self, start_year: int, end_year: int, leagues: list = ["00", "10"], out_dir: str = None):
        """
        Collects players, teams, and schedule data for each league and season in the given range.
        Skips files that already exist. Saves as CSV in the specified output directory (default: cache_dir).
        """
        out_dir = out_dir or self.config.cache_dir
        os.makedirs(out_dir, exist_ok=True)
        seasons = [f"{year}-{str(year+1)[-2:]}" for year in range(start_year, end_year)]
        for league_id in leagues:
            logger.info(f"Collecting data for league {league_id}...")
            for season in seasons:
                logger.info(f"  Season: {season}")
                # Players
                players_path = os.path.join(out_dir, f"players_{league_id}_{season}.csv")
                if not os.path.exists(players_path):
                    players_df = await self.get_players(season, league_id=league_id)
                    if players_df is not None and not players_df.empty:
                        players_df.to_csv(players_path, index=False)
                        logger.info(f"    Saved: {players_path}")
                    else:
                        logger.warning(f"    No player data for {league_id} {season}")
                else:
                    logger.info(f"    Skipped (exists): {players_path}")
                # Teams
                teams_path = os.path.join(out_dir, f"teams_{league_id}_{season}.csv")
                if not os.path.exists(teams_path):
                    teams_df = await self.get_team_statistics(season, league_id=league_id)
                    if teams_df is not None and not teams_df.empty:
                        teams_df.to_csv(teams_path, index=False)
                        logger.info(f"    Saved: {teams_path}")
                    else:
                        logger.warning(f"    No team data for {league_id} {season}")
                else:
                    logger.info(f"    Skipped (exists): {teams_path}")
                # Schedule
                schedule_path = os.path.join(out_dir, f"schedule_{league_id}_{season}.csv")
                if not os.path.exists(schedule_path):
                    schedule_df = await self.get_schedule(season, league_id=league_id)
                    if schedule_df is not None and not schedule_df.empty:
                        schedule_df.to_csv(schedule_path, index=False)
                        logger.info(f"    Saved: {schedule_path}")
                    else:
                        logger.warning(f"    No schedule data for {league_id} {season}")
                else:
                    logger.info(f"    Skipped (exists): {schedule_path}")

    async def collect_all_endpoints_bulk(self, start_year: int, end_year: int, leagues: list = ["00", "10"], out_dir: str = None):
        """
        Collects all player-level, team-level, and game-level endpoint data for every player, team, and game,
        for every league and season in the given range. Skips files that already exist. Saves as CSV in the output directory.
        """
        out_dir = out_dir or self.config.cache_dir
        os.makedirs(out_dir, exist_ok=True)
        seasons = [f"{year}-{str(year+1)[-2:]}" for year in range(start_year, end_year)]
        for league_id in leagues:
            logger.info(f"[BULK] Collecting ALL endpoint data for league {league_id}...")
            for season in seasons:
                logger.info(f"  [BULK] Season: {season}")
                # --- Load players, teams, schedule ---
                players_df = None
                teams_df = None
                schedule_df = None

                players_path = os.path.join(out_dir, f"players_{league_id}_{season}.csv")
                if os.path.exists(players_path):
                    players_df = pd.read_csv(players_path)
                else:
                    players_df = await self.get_players(season, league_id=league_id)
                    if players_df is not None and not players_df.empty:
                        players_df.to_csv(players_path, index=False)
                
                teams_path = os.path.join(out_dir, f"teams_{league_id}_{season}.csv")
                if os.path.exists(teams_path):
                    teams_df = pd.read_csv(teams_path)
                else:
                    teams_df = await self.get_team_statistics(season, league_id=league_id)
                    if teams_df is not None and not teams_df.empty:
                        teams_df.to_csv(teams_path, index=False)
                
                schedule_path = os.path.join(out_dir, f"schedule_{league_id}_{season}.csv")
                if os.path.exists(schedule_path):
                    schedule_df = pd.read_csv(schedule_path)
                else:
                    schedule_df = await self.get_schedule(season, league_id=league_id)
                    if schedule_df is not None and not schedule_df.empty:
                        schedule_df.to_csv(schedule_path, index=False)

                # --- Player-level endpoints ---
                if players_df is not None and not players_df.empty:
                    for _, player in players_df.iterrows():
                        player_id = int(player['PERSON_ID']) if 'PERSON_ID' in player else int(player['PLAYER_ID'])
                        player_methods = [
                            (self.get_player_game_logs, "game_logs"),
                            (self.get_player_general_splits, "general_splits"),
                            (self.get_player_clutch_stats, "clutch_stats"),
                            (self.get_player_shooting_locations, "shot_locations"),
                            (self.get_player_defensive_stats, "defensive_stats"),
                            (self.get_player_profilev2, "profile"),
                            (self.get_common_player_info, "commonplayerinfo"),
                            (self.get_league_hustle_stats_player, "hustle_stats_player"),
                        ]
                        for method, suffix in player_methods:
                            out_path = os.path.join(out_dir, f"player_{player_id}_{suffix}_{league_id}_{season}.csv")
                            if os.path.exists(out_path):
                                logger.info(f"    [SKIP] {out_path}")
                                continue
                            try:
                                if suffix == "profile" or suffix == "commonplayerinfo":
                                    df = await method(player_id, league_id=league_id)
                                elif suffix == "hustle_stats_player":
                                    df = await method(season, league_id=league_id)
                                else:
                                    df = await method(player_id, season, league_id=league_id)

                                if df is not None and not df.empty:
                                    df.to_csv(out_path, index=False)
                                    logger.info(f"    [SAVE] {out_path}")
                                else:
                                    logger.warning(f"    [NO DATA] {out_path}")
                            except Exception as e:
                                logger.warning(f"    [ERROR] {out_path}: {e}")
                # --- Team-level endpoints ---
                if teams_df is not None and not teams_df.empty:
                    for _, team in teams_df.iterrows():
                        team_id = int(team['TEAM_ID'])
                        team_methods = [
                            (self.get_team_statistics, "stats"),
                            (self.get_team_general_splits, "general_splits"),
                            (self.get_team_clutch_stats, "clutch_stats"),
                            (self.get_opponent_shooting, "opponent_shooting"),
                            (self.get_team_defensive_stats, "defensive_stats"),
                            (self.get_team_details, "details"),
                            (self.get_common_team_years, "commonteamyears"),
                            (self.get_team_game_log, "teamgamelog"),
                            (self.get_team_player_on_off_details, "teamplayeronoffdetails"),
                            (self.get_team_player_on_off_summary, "teamplayeronoffsummary"),
                            (self.get_league_hustle_stats_team, "hustle_stats_team"),
                        ]
                        for method, suffix in team_methods:
                            out_path = os.path.join(out_dir, f"team_{team_id}_{suffix}_{league_id}_{season}.csv")
                            if os.path.exists(out_path):
                                logger.info(f"    [SKIP] {out_path}")
                                continue
                            try:
                                if suffix == "details":
                                    df = await method(team_id, season, league_id=league_id)
                                elif suffix == "commonteamyears":
                                    df = await method(team_id, league_id=league_id)
                                elif suffix == "hustle_stats_team":
                                    df = await method(season, league_id=league_id)
                                else:
                                    df = await method(team_id, season, league_id=league_id)
                                if df is not None and not df.empty:
                                    df.to_csv(out_path, index=False)
                                    logger.info(f"    [SAVE] {out_path}")
                                else:
                                    logger.warning(f"    [NO DATA] {out_path}")
                            except Exception as e:
                                logger.warning(f"    [ERROR] {out_path}: {e}")
                # --- Game-level endpoints ---
                if schedule_df is not None and not schedule_df.empty:
                    for _, game in schedule_df.iterrows():
                        game_id = str(game['GAME_ID'])
                        game_methods = [
                            (self.get_game_box_score_traditional, "boxscore_traditional"),
                            (self.get_game_box_score_advanced, "boxscore_advanced"),
                            (self.get_box_score_scoring, "boxscore_scoring"),
                            (self.get_box_score_misc, "boxscore_misc"),
                            (self.get_box_score_four_factors, "boxscore_fourfactors"),
                            (self.get_box_score_usage, "boxscore_usage"),
                            (self.get_play_by_play_v2, "playbyplayv2"),
                            (self.get_game_rotation, "gamerotation"),
                        ]
                        for method, suffix in game_methods:
                            out_path = os.path.join(out_dir, f"game_{game_id}_{suffix}_{league_id}.csv")
                            if os.path.exists(out_path):
                                logger.info(f"    [SKIP] {out_path}")
                                continue
                            try:
                                df = await method(game_id, league_id=league_id)
                                if df is not None and not df.empty:
                                    df.to_csv(out_path, index=False)
                                    logger.info(f"    [SAVE] {out_path}")
                                else:
                                    logger.warning(f"    [NO DATA] {out_path}")
                            except Exception as e:
                                logger.warning(f"    [ERROR] {out_path}: {e}")
                # --- League-level endpoints (once per season) ---
                league_methods = [
                    (self.get_league_standings_v3, "leaguestandingsv3"),
                    (self.get_league_leaders, "leagueleaders"),
                    (self.get_league_dash_lineups, "leaguedashlineups"),
                    (self.get_league_player_on_details, "leagueplayerondetails"),
                    (self.get_draft_history, "drafthistory_overall"),
                ]
                for method, suffix in league_methods:
                    out_path = os.path.join(out_dir, f"league_{suffix}_{league_id}_{season}.csv")
                    if os.path.exists(out_path):
                        logger.info(f"    [SKIP] {out_path}")
                        continue
                    try:
                        if suffix == "leagueleaders":
                            df = await method(season, league_id=league_id, stat_category='PTS')
                        elif suffix == "drafthistory_overall":
                            df = await method(league_id=league_id)
                            out_path = os.path.join(out_dir, f"league_{suffix}_{league_id}.csv")
                        else:
                            df = await method(season, league_id=league_id)
                        
                        if df is not None and not df.empty:
                            df.to_csv(out_path, index=False)
                            logger.info(f"    [SAVE] {out_path}")
                        else:
                            logger.warning(f"    [NO DATA] {out_path}")
                    except Exception as e:
                        logger.warning(f"    [ERROR] {out_path}: {e}")
        logger.info("[BULK] Finished collecting all endpoint data.")

if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="Run BasketballDataLoader bulk data collection.")
    parser.add_argument('--start_year', type=int, default=datetime.now().year-2, help='Start year (e.g. 2023)')
    parser.add_argument('--end_year', type=int, default=datetime.now().year, help='End year (exclusive, e.g. 2025)')
    parser.add_argument('--leagues', nargs='+', default=["00", "10"], help='List of league IDs (default: NBA=00, WNBA=10)')
    parser.add_argument('--out_dir', type=str, default=None, help='Output directory for CSVs')
    parser.add_argument('--all_endpoints', action='store_true', help='Collect all endpoints (player/team/game)')
    args = parser.parse_args()

    loader = BasketballDataLoader()
    if args.all_endpoints:
        asyncio.run(loader.collect_all_endpoints_bulk(
            start_year=args.start_year,
            end_year=args.end_year,
            leagues=args.leagues,
            out_dir=args.out_dir
        ))
    else:
        asyncio.run(loader.collect_bulk_data(
            start_year=args.start_year,
            end_year=args.end_year,
            leagues=args.leagues,
            out_dir=args.out_dir
        ))