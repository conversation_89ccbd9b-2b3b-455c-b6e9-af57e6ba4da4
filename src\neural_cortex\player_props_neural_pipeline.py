#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Player Props Neural Training Pipeline
Advanced neural network training for individual player performance predictions
"""

# ...existing code...
import sys
import os
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from pathlib import Path
from datetime import datetime
import logging
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass, field
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from torch.utils.data import Dataset, DataLoader
import torch.nn.functional as F
# Add project root to path
sys.path.append('.')

from src.neural_cortex.neural_training_pipeline import TrainingConfig, EnhancedNeuralBasketballCore
from src.data.basketball_data_loader import BasketballDataLoader

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class PlayerPropsConfig(TrainingConfig):
    """Configuration for player props neural training"""
    # Player props specific settings
    prop_type: str = "points"  # points, rebounds, assists, steals, blocks, threes
    prediction_target: str = "regression"  # regression for exact values, classification for over/under
    
    # Enhanced features for player props
    include_player_features: bool = True
    include_matchup_features: bool = True
    include_situational_features: bool = True
    include_recent_form: bool = True

    # Model architecture adjustments for regression
    output_dim: int = 1  # Single value prediction
    use_batch_norm: bool = True
    output_activation: str = "relu"  # Output activation function
    hidden_dim: int = 128  # Hidden layer dimension
    num_layers: int = 3  # Number of hidden layers
    dropout_rate: float = 0.3  # Dropout rate
    loss_function: str = "mse"  # Loss function (mse, mae, huber)
    weight_decay: float = 0.01  # Weight decay for regularization
    patience: int = 5  # Early stopping patience
    output_activation: str = "relu"  # Output activation function
    hidden_dim: int = 128  # Hidden layer dimension
    num_layers: int = 3  # Number of hidden layers
    dropout_rate: float = 0.3  # Dropout rate
    
    def __post_init__(self):
        # Adjust model save path for player props
        if not hasattr(self, 'model_save_path') or self.model_save_path == "./models/neural_core":
            self.model_save_path = f"./models/player_props/{self.league.lower()}_{self.prop_type}"

class PlayerPropsNeuralNetwork(nn.Module):
    """Neural network specifically designed for player props prediction"""
    
    def __init__(self, input_dim: int, hidden_dim: int = 128, num_layers: int = 3, 
                 dropout_rate: float = 0.3, output_activation: str = "relu", 
                 use_batch_norm: bool = True):
        super(PlayerPropsNeuralNetwork, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        self.dropout_rate = dropout_rate
        self.output_activation = output_activation
        self.use_batch_norm = use_batch_norm
        
        # Build layers
        layers = []
        
        # Input layer
        layers.append(nn.Linear(input_dim, hidden_dim))
        if use_batch_norm:
            layers.append(nn.BatchNorm1d(hidden_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Dropout(dropout_rate))
        
        # Hidden layers
        for i in range(num_layers - 1):
            layers.append(nn.Linear(hidden_dim, hidden_dim))
            if use_batch_norm:
                layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))
        
        # Output layer
        layers.append(nn.Linear(hidden_dim, 1))

        # Output activation - FIXED: Use linear for regression
        if output_activation == "sigmoid":
            layers.append(nn.Sigmoid())  # For probabilities (0-1)
        elif output_activation == "relu":
            layers.append(nn.ReLU())  # For strictly positive values (use with caution)
        # Default: Linear activation (no activation) for regression - BEST for most cases
        
        self.network = nn.Sequential(*layers)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize network weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        """Forward pass"""
        return self.network(x)
    
    def forward_inference(self, x):
        """Forward pass for
          inference (handles batch norm for single samples)"""
        if x.size(0) == 1 and self.use_batch_norm:
            # For single sample inference, use eval mode
            self.eval()
            with torch.no_grad():
                return self.forward(x)
        else:
            return self.forward(x)

class PlayerPropsDataset(Dataset):
    """Dataset for player props training"""
    
    def __init__(self, data_path: str, config: PlayerPropsConfig, split: str = "train"):
        self.config = config
        self.split = split
        self.data_path = Path(data_path)
        self.prop_type = config.prop_type
        
        # Load and preprocess data
        self.data, self.targets, self.features = self._load_player_props_data()
        
        logger.info(f"📊 {split.upper()} dataset loaded: {len(self.data)} samples, {len(self.features)} features")
        logger.info(f"🎯 Target ({self.prop_type}): mean={np.mean(self.targets):.2f}, std={np.std(self.targets):.2f}")
    
    def _load_player_props_data(self) -> Tuple[np.ndarray, np.ndarray, List[str]]:
        """Load and preprocess player props data"""
        try:
            # Initialize data loader
            data_loader = BasketballDataLoader()
            
            # Load player performance data
            raw_data = data_loader.load_training_data(league=self.config.league)
            
            if raw_data is None or len(raw_data) == 0:
                raise ValueError(f"No data available for {self.config.league}")
            
            logger.info(f"📊 Loaded {len(raw_data)} raw records for {self.config.league}")
            
            # Process for player props
            processed_data = self._process_for_player_props(raw_data)
            
            # Extract features and targets
            features, targets = self._extract_features_and_targets(processed_data)
            
            return features, targets, self._get_feature_names()
            
        except Exception as e:
            logger.error(f"❌ Failed to load player props data: {e}")
            raise
    
    def _process_for_player_props(self, data: pd.DataFrame) -> pd.DataFrame:
        """Process raw data for player props prediction using REAL WNBA statistics"""
        logger.info(f"🔧 Processing data for {self.prop_type} prediction using REAL WNBA data...")

        # Load real WNBA training data for this prop type
        real_data_path = f"data/real_wnba_{self.prop_type}_training_data.csv"

        try:
            # Load real WNBA training data
            real_wnba_data = pd.read_csv(real_data_path)
            logger.info(f"✅ Loaded {len(real_wnba_data)} real WNBA {self.prop_type} records")

            # Use real WNBA data directly - targets are already in per-game format in the CSV
            # The 'target' column contains actual WNBA player per-game statistics
            processed_data = real_wnba_data.copy()

            # Rename target column to prop_target for consistency
            if 'target' in processed_data.columns:
                processed_data['prop_target'] = processed_data['target']
                processed_data = processed_data.drop('target', axis=1)

            # Convert season totals to per-game averages for target with proper filtering
            if self.prop_type in processed_data.columns and 'games_played' in processed_data.columns:
                # Filter out players with very few games (< 5 games to avoid division by small numbers)
                initial_count = len(processed_data)
                processed_data = processed_data[processed_data['games_played'] >= 5].copy()
                filtered_count = len(processed_data)
                logger.info(f"🔧 Filtered out {initial_count - filtered_count} players with < 5 games played")

                # Check if values are already per-game or season totals
                max_raw_value = processed_data[self.prop_type].max()

                # Define realistic per-game maximums for each stat
                per_game_maximums = {
                    'points': 50, 'rebounds': 25, 'assists': 15,
                    'steals': 5, 'blocks': 5, 'threes': 10
                }
                reasonable_max = per_game_maximums.get(self.prop_type, 50)

                if max_raw_value > reasonable_max:
                    # Values appear to be season totals, convert to per-game
                    processed_data['prop_target'] = processed_data[self.prop_type] / processed_data['games_played']
                    logger.info(f"📊 Converted {self.prop_type} season totals to per-game averages")
                else:
                    # Values appear to already be per-game
                    processed_data['prop_target'] = processed_data[self.prop_type]
                    logger.info(f"📊 Using {self.prop_type} values as-is (already per-game)")

                # Clip per-game values to realistic ranges
                clip_maximums = {
                    'points': 35, 'rebounds': 15, 'assists': 10,
                    'steals': 3, 'blocks': 3, 'threes': 6
                }
                clip_max = clip_maximums.get(self.prop_type, 35)
                processed_data['prop_target'] = processed_data['prop_target'].clip(upper=clip_max)
                logger.info(f"📊 Clipped {self.prop_type} values to realistic maximum of {clip_max}")

                # Log real data statistics
                min_val = processed_data['prop_target'].min()
                max_val = processed_data['prop_target'].max()
                mean_val = processed_data['prop_target'].mean()
                median_val = processed_data['prop_target'].median()
                logger.info(f"📈 Real WNBA {self.prop_type} per-game: min={min_val:.1f}, max={max_val:.1f}, mean={mean_val:.1f}, median={median_val:.1f}")

            # Ensure we have the required features for training
            required_features = [
                'points', 'rebounds', 'assists', 'steals', 'blocks', 'threes',
                'games_played', 'minutes_per_game', 'field_goal_percentage', 'free_throw_percentage',
                'age', 'scoring_tier', 'rebounding_tier', 'playmaking_tier', 'defensive_tier',
                'high_scorer', 'high_rebounder', 'high_assists', 'high_steals', 'high_blocks', 'high_threes',
                'points_per_minute', 'rebounds_per_minute', 'assists_per_minute', 'steals_per_minute',
                'blocks_per_minute', 'threes_per_minute', 'total_stats', 'defensive_stats', 'offensive_stats'
            ]

            # Check for missing features
            missing_features = [f for f in required_features if f not in processed_data.columns]
            if missing_features:
                logger.warning(f"⚠️ Missing features in real data: {missing_features}")

            logger.info(f"✅ Successfully processed {len(processed_data)} real WNBA {self.prop_type} records")
            return processed_data

        except FileNotFoundError:
            logger.error(f"❌ Real WNBA data file not found: {real_data_path}")
            logger.info("🔄 Falling back to complete real WNBA features file...")

            # Fallback to complete real WNBA features
            try:
                complete_data_path = "data/complete_real_wnba_features_with_metadata.csv"
                real_wnba_data = pd.read_csv(complete_data_path)
                logger.info(f"✅ Loaded {len(real_wnba_data)} complete real WNBA records")

                # Calculate per-game targets from season totals with proper filtering
                if self.prop_type in real_wnba_data.columns and 'games_played' in real_wnba_data.columns:
                    # Filter out players with very few games (< 5 games to avoid division by small numbers)
                    initial_count = len(real_wnba_data)
                    real_wnba_data = real_wnba_data[real_wnba_data['games_played'] >= 5].copy()
                    filtered_count = len(real_wnba_data)
                    logger.info(f"🔧 Filtered out {initial_count - filtered_count} players with < 5 games played")

                    # Check if values are already per-game or season totals
                    max_raw_value = real_wnba_data[self.prop_type].max()

                    # Define realistic per-game maximums for each stat
                    per_game_maximums = {
                        'points': 50, 'rebounds': 25, 'assists': 15,
                        'steals': 5, 'blocks': 5, 'threes': 10
                    }
                    reasonable_max = per_game_maximums.get(self.prop_type, 50)

                    if max_raw_value > reasonable_max:
                        # Values appear to be season totals, convert to per-game
                        real_wnba_data['prop_target'] = real_wnba_data[self.prop_type] / real_wnba_data['games_played']
                        logger.info(f"📊 Converted {self.prop_type} season totals to per-game averages")
                    else:
                        # Values appear to already be per-game
                        real_wnba_data['prop_target'] = real_wnba_data[self.prop_type]
                        logger.info(f"📊 Using {self.prop_type} values as-is (already per-game)")

                    # Clip per-game values to realistic ranges
                    clip_maximums = {
                        'points': 35, 'rebounds': 15, 'assists': 10,
                        'steals': 3, 'blocks': 3, 'threes': 6
                    }
                    clip_max = clip_maximums.get(self.prop_type, 35)
                    real_wnba_data['prop_target'] = real_wnba_data['prop_target'].clip(upper=clip_max)
                    logger.info(f"📊 Clipped {self.prop_type} values to realistic maximum of {clip_max}")

                    # Log real data statistics
                    min_val = real_wnba_data['prop_target'].min()
                    max_val = real_wnba_data['prop_target'].max()
                    mean_val = real_wnba_data['prop_target'].mean()
                    median_val = real_wnba_data['prop_target'].median()
                    logger.info(f"📈 Real WNBA {self.prop_type} per-game: min={min_val:.1f}, max={max_val:.1f}, mean={mean_val:.1f}, median={median_val:.1f}")

                    return real_wnba_data
                else:
                    logger.error(f"❌ Required columns not found in complete data: {self.prop_type}, games_played")
                    raise ValueError(f"Cannot process real data for {self.prop_type}")

            except Exception as e:
                logger.error(f"❌ Failed to load complete real WNBA data: {e}")
                raise ValueError(f"Cannot load real WNBA data for {self.prop_type} training")

        except Exception as e:
            logger.error(f"❌ Error processing real WNBA data: {e}")
            raise ValueError(f"Failed to process real WNBA data for {self.prop_type}")
    
    def _add_player_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add player-specific features"""
        # Player consistency (coefficient of variation)
        data['player_consistency'] = 1.0 / (1.0 + data['stat_value'].std() / (data['stat_value'].mean() + 1e-6))
        
        # Player tier (based on performance)
        data['player_tier'] = pd.qcut(data['stat_value'], q=5, labels=False, duplicates='drop')
        
        # Position encoding (if available)
        if 'position' in data.columns:
            data['position_encoded'] = pd.Categorical(data['position']).codes
        else:
            data['position_encoded'] = 0
        
        return data
    
    def _add_matchup_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add matchup-specific features"""
        # Opponent strength (based on rank_position)
        data['opponent_strength'] = 1.0 / (data['rank_position'] + 1)
        
        # Home/away advantage
        data['home_advantage'] = np.random.choice([0, 1], size=len(data), p=[0.5, 0.5])
        
        return data
    
    def _add_situational_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add situational features"""
        # Rest days (simulated)
        data['rest_days'] = np.random.choice([0, 1, 2, 3], size=len(data), p=[0.2, 0.4, 0.3, 0.1])
        
        # Back-to-back games
        data['back_to_back'] = (data['rest_days'] == 0).astype(int)
        
        # Season progression
        data['season_progress'] = np.random.uniform(0, 1, size=len(data))
        
        return data
    
    def _add_recent_form_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """Add recent form features"""
        # Recent performance trend (simulated)
        data['recent_form'] = np.random.normal(0, 0.2, size=len(data))
        
        # Hot/cold streak indicator
        data['hot_streak'] = (data['recent_form'] > 0.3).astype(int)
        data['cold_streak'] = (data['recent_form'] < -0.3).astype(int)
        
        return data
    
    def _extract_features_and_targets(self, data: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Extract features and targets from processed data with proper scaling and diagnostics"""
        logger.info(f"📊 Available columns: {list(data.columns)}")

        # Feature columns (exclude target and identifiers)
        exclude_cols = ['prop_target', 'game_id', 'player_id', 'team_id']
        feature_cols = [col for col in data.columns if col not in exclude_cols]

        # Select numerical features
        numerical_features = []
        for col in feature_cols:
            if col in data.columns:
                from pandas.api.types import is_numeric_dtype
                if is_numeric_dtype(data[col]):
                    numerical_features.append(col)
                else:
                    try:
                        pd.to_numeric(data[col], errors='raise')
                        numerical_features.append(col)
                    except (ValueError, TypeError):
                        logger.debug(f"Skipping non-numeric column: {col}")
                        continue

        logger.info(f"📊 Selected {len(numerical_features)} numerical features: {numerical_features}")

        # If no numerical features found, use basic features
        if len(numerical_features) == 0:
            logger.warning("⚠️ No numerical features found, using basic features")
            for col in data.columns:
                if col != 'prop_target' and data[col].dtype in ['int64', 'float64', 'int32', 'float32']:
                    numerical_features.append(col)

        # Ensure we have at least some features
        if len(numerical_features) == 0:
            logger.warning("⚠️ Creating synthetic features")
            data['feature_1'] = data.index.astype(float)
            data['feature_2'] = np.random.normal(0, 1, len(data))
            numerical_features = ['feature_1', 'feature_2']

        # Extract features and targets
        features = data[numerical_features].values
        targets = data['prop_target'].values

        # Handle missing values
        features = np.nan_to_num(features, nan=0.0, posinf=0.0, neginf=0.0)
        targets = np.nan_to_num(targets, nan=0.0, posinf=0.0, neginf=0.0)

        # Ensure targets are positive for props like points, rebounds
        if self.prop_type in ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']:
            targets = np.maximum(targets, 0.0)

        # --- DIAGNOSTICS: Target Distribution and Correlation ---
        logger.info(f"📊 Raw features shape: {features.shape}")
        logger.info(f"📊 Raw targets shape: {targets.shape}")
        logger.info(f"📊 Raw target range: {np.min(targets):.2f} to {np.max(targets):.2f}")
        logger.info(f"📊 Raw target mean: {np.mean(targets):.2f}, std: {np.std(targets):.2f}")
        logger.info(f"📊 Raw target unique values: {len(np.unique(targets))}")
        if np.std(targets) < 1e-3 or len(np.unique(targets)) < 5:
            logger.warning(f"⚠️ Target is nearly constant! Model will predict the mean. std={np.std(targets):.4f}, unique={len(np.unique(targets))}")

        # Correlation diagnostics
        try:
            df_diag = pd.DataFrame(features, columns=numerical_features)
            df_diag['target'] = targets
            corrs = df_diag.corr()['target'].drop('target')
            logger.info("📊 Feature-target correlations:")
            for feat, corr in corrs.items():
                logger.info(f"    {feat}: {corr:.3f}")
            max_corr = corrs.abs().max()
            if max_corr < 0.05:
                logger.warning(f"⚠️ All feature-target correlations are very low (max |corr|={max_corr:.3f}). Model will not learn meaningful differences.")
        except Exception as e:
            logger.warning(f"Could not compute feature-target correlations: {e}")

        return features, targets
    
    def _get_feature_names(self) -> List[str]:
        """Get feature names"""
        base_features = ['stat_value', 'rank_position']
        
        if self.config.include_player_features:
            base_features.extend(['player_consistency', 'player_tier', 'position_encoded'])
        
        if self.config.include_matchup_features:
            base_features.extend(['opponent_strength', 'home_advantage'])
        
        if self.config.include_situational_features:
            base_features.extend(['rest_days', 'back_to_back', 'season_progress'])
        
        if self.config.include_recent_form:
            base_features.extend(['recent_form', 'hot_streak', 'cold_streak'])
        
        return base_features
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        features = torch.FloatTensor(self.data[idx])
        target = torch.FloatTensor([self.targets[idx]])  # Single value target
        return features, target

class PlayerPropsTrainingPipeline:
    @staticmethod
    def predict_from_checkpoint(
        checkpoint_path: str,
        input_df: pd.DataFrame,
        device: str = None,
        return_confidence: bool = True
    ) -> pd.DataFrame:
        """
        Run inference using a saved checkpoint, enforcing feature alignment and scaling.
        Returns a DataFrame with predictions and (optionally) confidence/rationale.
        """
        import torch
        # Load checkpoint
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        feature_list = checkpoint.get('feature_list', None)
        if feature_list is None:
            logger.error(f"No feature_list found in checkpoint: {checkpoint_path}")
            raise ValueError("Feature list missing from checkpoint.")

        # Align features
        aligned_df = PlayerPropsTrainingPipeline.align_features_for_inference(input_df, feature_list)

        # Load feature scaler
        scaler_params = checkpoint.get('feature_scaler_params', None)
        if scaler_params is None:
            logger.error("No feature_scaler_params found in checkpoint.")
            raise ValueError("Feature scaler missing from checkpoint.")
        from sklearn.preprocessing import StandardScaler
        feature_scaler = StandardScaler()
        # Set scaler attributes
        for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
            if scaler_params.get(attr) is not None:
                setattr(feature_scaler, attr, scaler_params[attr])

        # Scale features
        X = feature_scaler.transform(aligned_df.values)

        # Load model
        input_dim = X.shape[1]
        model = PlayerPropsNeuralNetwork(
            input_dim=input_dim,
            hidden_dim=checkpoint['config'].get('hidden_dim', 128),
            num_layers=checkpoint['config'].get('num_layers', 3),
            dropout_rate=checkpoint['config'].get('dropout_rate', 0.3),
            output_activation=checkpoint['config'].get('output_activation', 'relu'),
            use_batch_norm=checkpoint['config'].get('use_batch_norm', True)
        )
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        if device is not None:
            model.to(device)

        # Predict
        with torch.no_grad():
            X_tensor = torch.tensor(X, dtype=torch.float32)
            outputs = model.forward_inference(X_tensor).cpu().numpy().flatten()

        # Unscale predictions
        target_scaler_params = checkpoint.get('target_scaler_params', None)
        if target_scaler_params is not None:
            target_scaler = StandardScaler()
            for attr in ['mean_', 'scale_', 'var_', 'n_features_in_', 'n_samples_seen_']:
                if target_scaler_params.get(attr) is not None:
                    setattr(target_scaler, attr, target_scaler_params[attr])
            predictions = target_scaler.inverse_transform(outputs.reshape(-1, 1)).flatten()
        else:
            predictions = outputs

        # Optionally, compute confidence/rationale (e.g., model output std, or just a placeholder)
        result_df = input_df.copy()
        result_df['prediction'] = predictions
        if return_confidence:
            # Simple confidence: use model output stddev or a fixed value (for demo)
            # In real use, could use dropout MC, ensemble, or output variance
            result_df['confidence'] = np.std(outputs)
            result_df['rationale'] = (
                "Prediction based on aligned features: "
                f"{feature_list}. Confidence is output stddev."
            )
        return result_df

class PlayerPropsNeuralPipeline:
    """Complete training pipeline for player props neural networks"""

    def __init__(self, config: PlayerPropsConfig, prop_type: str = None):
        self.config = config
        # Override prop_type if provided
        if prop_type:
            self.config.prop_type = prop_type
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        self.optimizer = None
        self.scheduler = None
        self.scaler = None

        # Training state
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.patience_counter = 0
        self.training_history = []

        # Setup directories
        self._setup_directories()

        logger.info(f"🏀 Player Props Training Pipeline initialized")
        logger.info(f"📊 Prop Type: {config.prop_type}")
        logger.info(f"🏆 League: {config.league}")
        logger.info(f"💻 Device: {self.device}")

    def _setup_directories(self):
        """Setup model save directories"""
        save_path = Path(self.config.model_save_path)
        save_path.mkdir(parents=True, exist_ok=True)
        logger.info(f"📁 Model save path: {save_path}")

    def _build_model(self) -> PlayerPropsNeuralNetwork:
        """Build the player props neural network"""
        model = PlayerPropsNeuralNetwork(
            input_dim=self.config.input_dim,
            hidden_dim=self.config.hidden_dim,
            num_layers=self.config.num_layers,
            dropout_rate=self.config.dropout_rate,
            output_activation=self.config.output_activation,
            use_batch_norm=self.config.use_batch_norm
        )
        return model.to(self.device)

    def _setup_optimizer(self) -> Tuple[optim.Optimizer, optim.lr_scheduler._LRScheduler]:
        """Setup optimizer and scheduler"""
        optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config.learning_rate,
            weight_decay=self.config.weight_decay
        )

        scheduler = optim.lr_scheduler.ReduceLROnPlateau(
            optimizer, mode='min', factor=0.5, patience=5
        )

        return optimizer, scheduler

    def _get_loss_function(self) -> nn.Module:
        """Get loss function based on config"""
        if self.config.loss_function == "mse":
            return nn.MSELoss()
        elif self.config.loss_function == "mae":
            return nn.L1Loss()
        elif self.config.loss_function == "huber":
            return nn.SmoothL1Loss()
        else:
            return nn.MSELoss()

    def prepare_data(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """Prepare training, validation, and test data loaders with proper scaling"""
        logger.info("📊 Preparing player props datasets...")

        # Create full dataset
        full_dataset = PlayerPropsDataset(
            data_path="", config=self.config, split="full"
        )

        # Update input dimension based on actual data
        self.config.input_dim = full_dataset.data.shape[1]
        logger.info(f"📊 Updated input_dim to {self.config.input_dim}")

        # Split data BEFORE scaling to prevent data leakage
        train_size = int(0.7 * len(full_dataset))
        val_size = int(0.15 * len(full_dataset))
        test_size = len(full_dataset) - train_size - val_size

        train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
            full_dataset, [train_size, val_size, test_size]
        )

        # Extract training data for fitting scaler
        train_features = []
        train_targets = []
        for i in range(len(train_dataset)):
            features, target = train_dataset[i]
            train_features.append(features.numpy())
            train_targets.append(target.numpy())

        train_features = np.array(train_features)
        train_targets = np.array(train_targets)

        # Fit scalers on training data only
        from sklearn.preprocessing import StandardScaler
        self.feature_scaler = StandardScaler()
        self.target_scaler = StandardScaler()

        # Fit feature scaler
        self.feature_scaler.fit(train_features)

        # Fit target scaler (reshape for sklearn)
        self.target_scaler.fit(train_targets.reshape(-1, 1))

        logger.info(f"📊 Feature scaling: mean={self.feature_scaler.mean_[:3]}, std={self.feature_scaler.scale_[:3]}")
        logger.info(f"📊 Target scaling: mean={self.target_scaler.mean_[0]:.2f}, std={self.target_scaler.scale_[0]:.2f}")

        # Apply scaling to all datasets
        self._apply_scaling_to_dataset(train_dataset)
        self._apply_scaling_to_dataset(val_dataset)
        self._apply_scaling_to_dataset(test_dataset)

        # Create data loaders
        train_loader = DataLoader(
            train_dataset, batch_size=self.config.batch_size,
            shuffle=True, num_workers=0
        )
        val_loader = DataLoader(
            val_dataset, batch_size=self.config.batch_size,
            shuffle=False, num_workers=0
        )
        test_loader = DataLoader(
            test_dataset, batch_size=self.config.batch_size,
            shuffle=False, num_workers=0
        )

        logger.info(f"📊 Train: {len(train_dataset)} samples")
        logger.info(f"📊 Val: {len(val_dataset)} samples")
        logger.info(f"📊 Test: {len(test_dataset)} samples")

        return train_loader, val_loader, test_loader

    def _apply_scaling_to_dataset(self, dataset):
        """Apply feature and target scaling to a dataset"""
        for i in range(len(dataset)):
            # Get original data
            features, target = dataset.dataset.data[dataset.indices[i]], dataset.dataset.targets[dataset.indices[i]]

            # Scale features
            scaled_features = self.feature_scaler.transform(features.reshape(1, -1)).flatten()

            # Scale target
            scaled_target = self.target_scaler.transform([[target]])[0][0]

            # Update dataset (this modifies the underlying data)
            dataset.dataset.data[dataset.indices[i]] = scaled_features
            dataset.dataset.targets[dataset.indices[i]] = scaled_target

    def train_epoch(self, train_loader: DataLoader, criterion: nn.Module) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        total_loss = 0.0
        total_mae = 0.0
        num_batches = 0

        for batch_idx, (data, targets) in enumerate(train_loader):
            data = data.to(self.device)
            targets = targets.to(self.device).squeeze()

            # Forward pass
            self.optimizer.zero_grad()
            outputs = self.model(data).squeeze()

            # Calculate loss
            loss = criterion(outputs, targets)

            # Backward pass
            loss.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)

            self.optimizer.step()

            # Track metrics
            total_loss += loss.item()
            total_mae += F.l1_loss(outputs, targets).item()
            num_batches += 1

        return {
            'loss': total_loss / num_batches,
            'mae': total_mae / num_batches
        }

    def validate_epoch(self, val_loader: DataLoader, criterion: nn.Module) -> Dict[str, float]:
        """Validate for one epoch"""
        self.model.eval()
        total_loss = 0.0
        total_mae = 0.0
        num_batches = 0

        with torch.no_grad():
            for data, targets in val_loader:
                data = data.to(self.device)
                targets = targets.to(self.device).squeeze()

                # Forward pass
                outputs = self.model(data).squeeze()

                # Calculate loss
                loss = criterion(outputs, targets)

                # Track metrics
                total_loss += loss.item()
                total_mae += F.l1_loss(outputs, targets).item()
                num_batches += 1

        return {
            'loss': total_loss / num_batches,
            'mae': total_mae / num_batches
        }

    async def train(self) -> Dict[str, Any]:
        """Main training loop"""
        logger.info(f"🚀 Starting {self.config.prop_type} neural training for {self.config.league}")

        # Prepare data
        train_loader, val_loader, test_loader = self.prepare_data()

        # Build model
        self.model = self._build_model()

        # Setup training
        self.optimizer, self.scheduler = self._setup_optimizer()
        criterion = self._get_loss_function()

        logger.info(f"📊 Model: {sum(p.numel() for p in self.model.parameters())} parameters")
        logger.info(f"📊 Loss function: {self.config.loss_function}")


        # Training loop
        for epoch in range(self.config.num_epochs):
            self.current_epoch = epoch

            # Train
            train_metrics = self.train_epoch(train_loader, criterion)

            # Validate
            val_metrics = self.validate_epoch(val_loader, criterion)

            # Optionally, compute R^2 on validation set for diagnostics
            try:
                self.model.eval()
                all_val_preds = []
                all_val_targets = []
                with torch.no_grad():
                    for data, targets in val_loader:
                        data = data.to(self.device)
                        targets = targets.to(self.device).squeeze()
                        outputs = self.model(data).squeeze()
                        all_val_preds.extend(outputs.cpu().numpy())
                        all_val_targets.extend(targets.cpu().numpy())
                import numpy as np
                from sklearn.metrics import r2_score
                val_r2 = r2_score(np.array(all_val_targets), np.array(all_val_preds))
                logger.info(f"Epoch {epoch:3d}: Validation R² (scaled): {val_r2:.4f}")
            except Exception as e:
                logger.warning(f"Could not compute validation R²: {e}")

            # Update scheduler
            self.scheduler.step(val_metrics['loss'])

            # Track progress
            self.training_history.append({
                'epoch': epoch,
                'train_loss': train_metrics['loss'],
                'train_mae': train_metrics['mae'],
                'val_loss': val_metrics['loss'],
                'val_mae': val_metrics['mae']
            })

            # Print progress
            if epoch % 5 == 0 or epoch == self.config.num_epochs - 1:
                logger.info(
                    f"Epoch {epoch:3d}/{self.config.num_epochs}: "
                    f"Train Loss: {train_metrics['loss']:.4f}, "
                    f"Val Loss: {val_metrics['loss']:.4f}, "
                    f"Train MAE: {train_metrics['mae']:.4f}, "
                    f"Val MAE: {val_metrics['mae']:.4f}"
                )

            # Early stopping and model saving
            if val_metrics['loss'] < self.best_val_loss:
                self.best_val_loss = val_metrics['loss']
                self.patience_counter = 0
                self._save_checkpoint(epoch, is_best=True)
            else:
                self.patience_counter += 1

            if self.patience_counter >= self.config.early_stopping_patience:
                logger.info(f"⏹️ Early stopping at epoch {epoch}")
                break

        # Final evaluation
        test_metrics = self.evaluate(test_loader)

        logger.info("🎉 Training completed!")
        logger.info(f"📊 Best validation loss: {self.best_val_loss:.4f}")
        logger.info(f"📊 Test MAE: {test_metrics['mae']:.4f}")
        logger.info(f"📊 Test R²: {test_metrics['r2']:.4f}")

        return {
            'success': True,  # Indicate successful training completion
            'best_val_loss': self.best_val_loss,
            'test_metrics': test_metrics,
            'training_history': self.training_history,
            'total_epochs': self.current_epoch + 1,
            'final_accuracy': test_metrics.get('r2', 0.0) * 100  # Convert R² to percentage for display
        }

    def evaluate(self, test_loader: DataLoader) -> Dict[str, float]:
        """Evaluate model on test set with proper unscaling"""
        self.model.eval()
        all_predictions = []
        all_targets = []

        with torch.no_grad():
            for data, targets in test_loader:
                data = data.to(self.device)
                targets = targets.to(self.device).squeeze()

                outputs = self.model(data).squeeze()

                all_predictions.extend(outputs.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())

        # Convert to arrays
        predictions = np.array(all_predictions)
        targets = np.array(all_targets)

        # CRITICAL: Unscale predictions and targets back to original scale
        if hasattr(self, 'target_scaler') and self.target_scaler is not None:
            predictions_unscaled = self.target_scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
            targets_unscaled = self.target_scaler.inverse_transform(targets.reshape(-1, 1)).flatten()

            logger.info(f"📊 Scaled predictions: mean={np.mean(predictions):.2f}, std={np.std(predictions):.2f}")
            logger.info(f"📊 Unscaled predictions: mean={np.mean(predictions_unscaled):.2f}, std={np.std(predictions_unscaled):.2f}")
            logger.info(f"📊 Unscaled targets: mean={np.mean(targets_unscaled):.2f}, std={np.std(targets_unscaled):.2f}")

            # Use unscaled values for metrics
            predictions = predictions_unscaled
            targets = targets_unscaled
        else:
            logger.warning("⚠️ No target scaler found - using raw predictions")

        # Calculate metrics on original scale
        mae = mean_absolute_error(targets, predictions)
        mse = mean_squared_error(targets, predictions)
        rmse = np.sqrt(mse)
        r2 = r2_score(targets, predictions)

        # Calculate percentage accuracy with realistic thresholds for sports data
        percentage_errors = np.abs((predictions - targets) / (targets + 1e-6)) * 100
        accuracy_10pct = np.mean(percentage_errors <= 10) * 100  # Very strict
        accuracy_25pct = np.mean(percentage_errors <= 25) * 100  # Reasonable for sports
        accuracy_50pct = np.mean(percentage_errors <= 50) * 100  # Loose but meaningful

        # Calculate absolute error thresholds (more intuitive for sports)
        # Ensure abs_errors is always a numeric array
        if isinstance(predictions, (pd.DataFrame, pd.Series)):
            predictions_arr = predictions.values
        else:
            predictions_arr = np.asarray(predictions)
        if isinstance(targets, (pd.DataFrame, pd.Series)):
            targets_arr = targets.values
        else:
            targets_arr = np.asarray(targets)
        abs_errors = np.abs(predictions_arr - targets_arr)
        accuracy_1pt = np.mean(abs_errors <= 1.0) * 100   # Within 1 point/rebound/assist
        accuracy_2pt = np.mean(abs_errors <= 2.0) * 100   # Within 2 points/rebounds/assists
        accuracy_3pt = np.mean(abs_errors <= 3.0) * 100   # Within 3 points/rebounds/assists

        return {
            'mae': mae,
            'mse': mse,
            'rmse': rmse,
            'r2': r2,
            'accuracy_10pct': accuracy_10pct,
            'accuracy_25pct': accuracy_25pct,
            'accuracy_50pct': accuracy_50pct,
            'accuracy_1pt': accuracy_1pt,
            'accuracy_2pt': accuracy_2pt,
            'accuracy_3pt': accuracy_3pt,
            'mean_prediction': np.mean(predictions_arr),
            'mean_target': np.mean(targets_arr),
            'median_abs_error': np.median(abs_errors),
            'std_prediction': np.std(predictions_arr),
            'std_target': np.std(targets_arr)
        }

    def _save_checkpoint(self, epoch: int, is_best: bool = False):
        """Save model checkpoint, including feature and target scalers and feature list"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_val_loss': self.best_val_loss,
            'config': self.config.__dict__,
            'prop_type': self.config.prop_type
        }

        # Save scaler parameters
        if hasattr(self, 'feature_scaler') and self.feature_scaler is not None:
            checkpoint['feature_scaler_params'] = {
                'mean_': self.feature_scaler.mean_.tolist() if hasattr(self.feature_scaler, 'mean_') else None,
                'scale_': self.feature_scaler.scale_.tolist() if hasattr(self.feature_scaler, 'scale_') else None,
                'var_': self.feature_scaler.var_.tolist() if hasattr(self.feature_scaler, 'var_') else None,
                'n_features_in_': self.feature_scaler.n_features_in_ if hasattr(self.feature_scaler, 'n_features_in_') else None,
                'n_samples_seen_': int(self.feature_scaler.n_samples_seen_) if hasattr(self.feature_scaler, 'n_samples_seen_') else None
            }

        if hasattr(self, 'target_scaler') and self.target_scaler is not None:
            checkpoint['target_scaler_params'] = {
                'mean_': self.target_scaler.mean_.tolist() if hasattr(self.target_scaler, 'mean_') else None,
                'scale_': self.target_scaler.scale_.tolist() if hasattr(self.target_scaler, 'scale_') else None,
                'var_': self.target_scaler.var_.tolist() if hasattr(self.target_scaler, 'var_') else None,
                'n_features_in_': self.target_scaler.n_features_in_ if hasattr(self.target_scaler, 'n_features_in_') else None,
                'n_samples_seen_': int(self.target_scaler.n_samples_seen_) if hasattr(self.target_scaler, 'n_samples_seen_') else None
            }

        # Save the feature list used for training (order matters!)
        # Try to get the feature list from the dataset, config, or fallback
        feature_list = None
        if hasattr(self, 'feature_list') and self.feature_list is not None:
            feature_list = self.feature_list
        elif hasattr(self, 'full_dataset') and hasattr(self.full_dataset, 'features'):
            feature_list = self.full_dataset.features
        elif hasattr(self.config, 'feature_list'):
            feature_list = self.config.feature_list
        # If still None, try to infer from scaler
        if feature_list is None and hasattr(self, 'feature_scaler') and hasattr(self.feature_scaler, 'feature_names_in_'):
            feature_list = list(self.feature_scaler.feature_names_in_)
        if feature_list is not None:
            checkpoint['feature_list'] = feature_list
            self.feature_list = feature_list  # Ensure it's set for this object
        else:
            logger.warning("Could not determine feature list for checkpoint!")

        # Regular checkpoint
        checkpoint_path = Path(self.config.model_save_path) / f"checkpoint_epoch_{epoch}.pt"
        torch.save(checkpoint, checkpoint_path)

        # Best model
        if is_best:
            best_path = Path(self.config.model_save_path) / f"best_{self.config.prop_type}_model.pt"
            torch.save(checkpoint, best_path)
            logger.info(f"💾 Best {self.config.prop_type} model saved at epoch {epoch} (with scalers and feature list)")

    @staticmethod
    def load_feature_list_from_checkpoint(checkpoint_path):
        """Load feature list from a saved model checkpoint"""
        import torch
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        feature_list = checkpoint.get('feature_list', None)
        if feature_list is None:
            logger.warning(f"No feature_list found in checkpoint: {checkpoint_path}")
        return feature_list

    @staticmethod
    def align_features_for_inference(input_df: pd.DataFrame, feature_list: list) -> pd.DataFrame:
        """Select and order features for inference to match training feature list"""
        missing = [f for f in feature_list if f not in input_df.columns]
        extra = [f for f in input_df.columns if f not in feature_list]
        if missing:
            logger.warning(f"⚠️ Missing features for inference: {missing}")
        if extra:
            logger.info(f"ℹ️ Extra features in inference data (will be ignored): {extra}")
        # Only use features in feature_list, in order
        aligned = input_df.reindex(columns=feature_list, fill_value=0.0)
        return aligned

def create_player_props_config(league: str = "WNBA", prop_type: str = "points",
                              **kwargs) -> PlayerPropsConfig:
    """Create optimized player props training configuration"""

    # Base configuration parameters
    base_params = {
        'league': league,
        'prop_type': prop_type,

        # Model architecture
        'model_type': "player_props_neural",
        'hidden_dim': 128,
        'num_layers': 3,
        'dropout_rate': 0.3,
        'use_batch_norm': True,

        # Training parameters
        'batch_size': 64,
        'learning_rate': 0.001,
        'weight_decay': 0.01,
        'num_epochs': 100,
        'early_stopping_patience': 15,

        # Player props specific
        'prediction_target': "regression",
        'loss_function': "mse",
        'output_activation': "relu",

        # Feature engineering
        'include_player_features': True,
        'include_matchup_features': True,
        'include_situational_features': True,
        'include_recent_form': True,

        # Data settings
        'min_games_played': 10,
        'lookback_games': 20
    }

    # Override with any provided kwargs (avoiding conflicts)
    for key, value in kwargs.items():
        base_params[key] = value

    # Create configuration
    config = PlayerPropsConfig(**base_params)

    return config
