#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Retrain ALL Player Props Models with Real WNBA Data
This script replaces synthetic target generation with actual WNBA player statistics
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add project root to path
sys.path.append('.')

from src.neural_cortex.player_props_neural_pipeline import PlayerPropsNeuralPipeline, PlayerPropsConfig

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# All player props to retrain
PLAYER_PROPS = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'threes']

async def retrain_single_prop(prop_type: str) -> dict:
    """Retrain a single player prop model with real WNBA data"""
    logger.info(f"🚀 Starting {prop_type} model retraining with REAL WNBA data...")
    
    try:
        # Create config for this prop type
        config = PlayerPropsConfig()
        config.prop_type = prop_type
        config.league = 'WNBA'
        config.epochs = 15  # Sufficient training
        config.batch_size = 32
        config.learning_rate = 0.001
        config.patience = 5
        
        # Initialize pipeline
        pipeline = PlayerPropsNeuralPipeline(config, prop_type)
        logger.info(f"✅ {prop_type} pipeline initialized")
        
        # Train the model
        start_time = datetime.now()
        result = await pipeline.train()
        end_time = datetime.now()
        
        training_time = (end_time - start_time).total_seconds()
        
        logger.info(f"✅ {prop_type} model training completed in {training_time:.1f}s")
        
        # Check if model was saved
        model_path = f"models/neural_models/player_props_{prop_type}_model.pth"
        if os.path.exists(model_path):
            logger.info(f"✅ {prop_type} model saved successfully")
        else:
            logger.warning(f"⚠️ {prop_type} model file not found at expected location")
        
        return {
            'prop_type': prop_type,
            'success': True,
            'training_time': training_time,
            'result': result
        }
        
    except Exception as e:
        logger.error(f"❌ {prop_type} training failed: {str(e)}")
        return {
            'prop_type': prop_type,
            'success': False,
            'error': str(e)
        }

async def retrain_all_props():
    """Retrain all player props models with real WNBA data"""
    logger.info("🎯 STARTING COMPLETE PLAYER PROPS RETRAINING WITH REAL WNBA DATA")
    logger.info(f"📊 Props to retrain: {PLAYER_PROPS}")
    
    results = []
    total_start_time = datetime.now()
    
    # Train each prop sequentially to avoid memory issues
    for prop_type in PLAYER_PROPS:
        result = await retrain_single_prop(prop_type)
        results.append(result)
        
        # Brief pause between models
        await asyncio.sleep(2)
    
    total_end_time = datetime.now()
    total_time = (total_end_time - total_start_time).total_seconds()
    
    # Summary report
    logger.info("🏆 RETRAINING COMPLETE - SUMMARY REPORT")
    logger.info(f"⏱️ Total time: {total_time:.1f}s")
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    logger.info(f"✅ Successful: {len(successful)}/{len(PLAYER_PROPS)}")
    for result in successful:
        logger.info(f"   ✅ {result['prop_type']}: {result['training_time']:.1f}s")
    
    if failed:
        logger.error(f"❌ Failed: {len(failed)}/{len(PLAYER_PROPS)}")
        for result in failed:
            logger.error(f"   ❌ {result['prop_type']}: {result['error']}")
    
    if len(successful) == len(PLAYER_PROPS):
        logger.info("🎉 ALL PLAYER PROPS MODELS SUCCESSFULLY RETRAINED WITH REAL WNBA DATA!")
        logger.info("🔥 Models now use actual WNBA player statistics instead of synthetic targets")
        logger.info("📈 Predictions should now be realistic (1-28 points, 0-13 rebounds, etc.)")
    else:
        logger.warning(f"⚠️ Only {len(successful)}/{len(PLAYER_PROPS)} models successfully retrained")
    
    return results

if __name__ == "__main__":
    print("🏀 HYPER MEDUSA NEURAL VAULT - Real Data Retraining")
    print("=" * 60)
    print("🎯 Replacing synthetic targets with REAL WNBA player statistics")
    print("📊 Training all 6 player props models with actual game data")
    print("=" * 60)
    
    # Run the retraining
    results = asyncio.run(retrain_all_props())
    
    print("\n" + "=" * 60)
    print("🏆 RETRAINING COMPLETED")
    print("🔥 Neural models now trained on REAL WNBA data!")
    print("=" * 60)
